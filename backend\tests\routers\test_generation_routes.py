"""
📝 [生成] 持久化路由测试
测试基于数据库持久化的故事圣经生成、章节生成等API接口
"""

import pytest
import asyncio
from httpx import AsyncClient
from fastapi import FastAPI
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any

from app.core.database import get_database_session, init_database
from app.routers.generation import router
from app.models.story_bible import StoryGenre, AIProvider, GenerationStatus
from app.core.config import log_debug, log_info, log_error


# 创建测试应用
def create_test_app() -> FastAPI:
    """创建测试用的FastAPI应用"""
    app = FastAPI()
    app.include_router(router)
    return app


@pytest.fixture
async def test_app():
    """测试应用fixture"""
    app = create_test_app()
    await init_database()
    return app


@pytest.fixture
async def async_client(test_app):
    """异步HTTP客户端fixture"""
    from httpx import ASGITransport
    transport = ASGITransport(app=test_app)
    async with AsyncClient(transport=transport, base_url="http://test") as client:
        yield client


@pytest.fixture
def sample_bible_request():
    """示例故事圣经请求数据"""
    return {
        "title": "测试小说：魔法学院的奇幻冒险",
        "genre": "fantasy",
        "theme": "成长与友谊的奇幻冒险故事，探索青春期的成长历程",
        "protagonist": "年轻的魔法师艾莉丝，一个充满好奇心和勇气的少女",
        "setting": "现代魔法学院，一个充满神秘色彩的教育机构",
        "plot_outline": "主角艾莉丝在魔法学院学习各种魔法技能，结交志同道合的朋友，经历各种冒险，最终成长为强大的魔法师并拯救学院免受邪恶势力的威胁",
        "target_audience": "青少年读者群体",
        "writing_style": "轻松幽默的叙述风格",
        "ai_provider": "zhipu",
        "temperature": 0.8,
        "max_tokens": 3000
    }


@pytest.fixture
def sample_chapter_request():
    """示例章节请求数据"""
    return {
        "story_bible_id": "test-bible-001",
        "chapter_number": 1,
        "chapter_title": "第一章：入学第一天",
        "chapter_outline": "主角艾莉丝第一天到达魔法学院，遇到室友和老师",
        "previous_chapter_summary": None,
        "character_development": "艾莉丝从紧张变为兴奋",
        "plot_requirements": "介绍学院环境和主要角色",
        "target_word_count": 2000,
        "ai_provider": "zhipu",
        "temperature": 0.8,
        "max_tokens": 4000
    }


class TestPersistenceRoutes:
    """持久化路由测试类"""
    
    async def test_generate_story_bible(self, async_client: AsyncClient, sample_bible_request: Dict[str, Any]):
        """💾 [数据库] 测试生成故事圣经API"""
        log_debug("测试", "开始测试生成故事圣经API")
        
        response = await async_client.post(
            "/api/v1/generate-bible",
            json=sample_bible_request
        )

        # 如果状态码不是200，打印响应内容以便调试
        if response.status_code != 200:
            log_error("测试", "API请求失败", 状态码=response.status_code, 响应内容=response.text)
            print(f"响应状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            print(f"请求数据: {sample_bible_request}")

        assert response.status_code == 200, f"期望状态码200，实际得到{response.status_code}，响应: {response.text}"
        data = response.json()
        
        # 验证响应结构
        assert "id" in data
        assert data["task_type"] == "story_bible"
        assert data["status"] == "pending"
        assert data["ai_provider"] == "zhipu"
        assert "created_at" in data
        assert "updated_at" in data
        assert "request_data" in data
        
        # 验证请求数据
        request_data = data["request_data"]
        assert request_data["title"] == sample_bible_request["title"]
        assert request_data["genre"] == sample_bible_request["genre"]
        assert request_data["theme"] == sample_bible_request["theme"]
        
        log_info("测试", "故事圣经生成API测试通过", 任务ID=data["id"])
        return data["id"]  # 返回任务ID供后续测试使用
    
    async def test_get_story_bible(self, async_client: AsyncClient, sample_bible_request: Dict[str, Any]):
        """💾 [数据库] 测试获取故事圣经API"""
        log_debug("测试", "开始测试获取故事圣经API")
        
        # 先创建一个故事圣经
        create_response = await async_client.post(
            "/api/v1/persistence/generate-bible",
            json=sample_bible_request
        )
        assert create_response.status_code == 200
        bible_id = create_response.json()["id"]
        
        # 获取故事圣经详情
        response = await async_client.get(f"/api/v1/persistence/bible/{bible_id}")
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证响应结构
        assert data["id"] == bible_id
        assert data["title"] == sample_bible_request["title"]
        assert data["genre"] == sample_bible_request["genre"]
        assert data["theme"] == sample_bible_request["theme"]
        assert data["protagonist"] == sample_bible_request["protagonist"]
        assert data["setting"] == sample_bible_request["setting"]
        assert data["plot_outline"] == sample_bible_request["plot_outline"]
        assert data["status"] == "pending"
        assert "created_at" in data
        assert "updated_at" in data
        
        log_info("测试", "获取故事圣经API测试通过", 故事圣经ID=bible_id)
    
    async def test_list_story_bibles(self, async_client: AsyncClient, sample_bible_request: Dict[str, Any]):
        """💾 [数据库] 测试获取故事圣经列表API"""
        log_debug("测试", "开始测试获取故事圣经列表API")
        
        # 先创建几个故事圣经
        bible_ids = []
        for i in range(3):
            request_data = sample_bible_request.copy()
            request_data["title"] = f"测试小说{i+1}"
            
            create_response = await async_client.post(
                "/api/v1/persistence/generate-bible",
                json=request_data
            )
            assert create_response.status_code == 200
            bible_ids.append(create_response.json()["id"])
        
        # 获取列表
        response = await async_client.get("/api/v1/persistence/bibles?limit=10&offset=0")
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证响应结构
        assert "total" in data
        assert "items" in data
        assert "limit" in data
        assert "offset" in data
        assert data["limit"] == 10
        assert data["offset"] == 0
        assert len(data["items"]) >= 3  # 至少包含我们创建的3个
        
        # 验证列表项结构
        for item in data["items"]:
            assert "id" in item
            assert "title" in item
            assert "genre" in item
            assert "status" in item
            assert "created_at" in item
        
        log_info("测试", "获取故事圣经列表API测试通过", 返回数量=len(data["items"]))
    
    async def test_generate_chapter_without_bible(self, async_client: AsyncClient, sample_chapter_request: Dict[str, Any]):
        """💾 [数据库] 测试在没有故事圣经的情况下生成章节"""
        log_debug("测试", "开始测试无效故事圣经ID的章节生成")
        
        # 使用不存在的故事圣经ID
        request_data = sample_chapter_request.copy()
        request_data["story_bible_id"] = "non-existent-bible-id"
        
        response = await async_client.post(
            "/api/v1/persistence/generate-chapter",
            json=request_data
        )
        
        assert response.status_code == 404
        data = response.json()
        assert "detail" in data
        assert "未找到故事圣经" in data["detail"]
        
        log_info("测试", "无效故事圣经ID测试通过")
    
    async def test_generate_chapter_with_bible(self, async_client: AsyncClient, 
                                             sample_bible_request: Dict[str, Any],
                                             sample_chapter_request: Dict[str, Any]):
        """💾 [数据库] 测试有效的章节生成"""
        log_debug("测试", "开始测试有效的章节生成API")
        
        # 先创建故事圣经
        bible_response = await async_client.post(
            "/api/v1/persistence/generate-bible",
            json=sample_bible_request
        )
        assert bible_response.status_code == 200
        bible_id = bible_response.json()["id"]
        
        # 创建章节
        chapter_request = sample_chapter_request.copy()
        chapter_request["story_bible_id"] = bible_id
        
        response = await async_client.post(
            "/api/v1/persistence/generate-chapter",
            json=chapter_request
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证响应结构
        assert "id" in data
        assert data["task_type"] == "chapter"
        assert data["status"] == "pending"
        assert data["ai_provider"] == "zhipu"
        assert "created_at" in data
        assert "updated_at" in data
        assert "request_data" in data
        
        # 验证请求数据
        request_data = data["request_data"]
        assert request_data["story_bible_id"] == bible_id
        assert request_data["chapter_number"] == 1
        assert request_data["chapter_title"] == sample_chapter_request["chapter_title"]
        
        log_info("测试", "章节生成API测试通过", 任务ID=data["id"], 故事圣经ID=bible_id)
        return data["id"]  # 返回章节ID供后续测试使用
    
    async def test_get_chapter(self, async_client: AsyncClient,
                              sample_bible_request: Dict[str, Any],
                              sample_chapter_request: Dict[str, Any]):
        """💾 [数据库] 测试获取章节API"""
        log_debug("测试", "开始测试获取章节API")
        
        # 先创建故事圣经和章节
        bible_response = await async_client.post(
            "/api/v1/persistence/generate-bible",
            json=sample_bible_request
        )
        bible_id = bible_response.json()["id"]
        
        chapter_request = sample_chapter_request.copy()
        chapter_request["story_bible_id"] = bible_id
        
        chapter_response = await async_client.post(
            "/api/v1/persistence/generate-chapter",
            json=chapter_request
        )
        chapter_id = chapter_response.json()["id"]
        
        # 获取章节详情
        response = await async_client.get(f"/api/v1/persistence/chapter/{chapter_id}")
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证响应结构
        assert data["id"] == chapter_id
        assert data["story_bible_id"] == bible_id
        assert data["chapter_number"] == 1
        assert data["chapter_title"] == sample_chapter_request["chapter_title"]
        assert data["chapter_outline"] == sample_chapter_request["chapter_outline"]
        assert data["target_word_count"] == sample_chapter_request["target_word_count"]
        assert data["status"] == "pending"
        assert "created_at" in data
        assert "updated_at" in data
        
        log_info("测试", "获取章节API测试通过", 章节ID=chapter_id)
    
    async def test_list_bible_chapters(self, async_client: AsyncClient,
                                      sample_bible_request: Dict[str, Any],
                                      sample_chapter_request: Dict[str, Any]):
        """💾 [数据库] 测试获取故事圣经章节列表API"""
        log_debug("测试", "开始测试获取故事圣经章节列表API")
        
        # 先创建故事圣经
        bible_response = await async_client.post(
            "/api/v1/persistence/generate-bible",
            json=sample_bible_request
        )
        bible_id = bible_response.json()["id"]
        
        # 创建多个章节
        chapter_ids = []
        for i in range(3):
            chapter_request = sample_chapter_request.copy()
            chapter_request["story_bible_id"] = bible_id
            chapter_request["chapter_number"] = i + 1
            chapter_request["chapter_title"] = f"第{i+1}章：测试章节"
            
            chapter_response = await async_client.post(
                "/api/v1/persistence/generate-chapter",
                json=chapter_request
            )
            chapter_ids.append(chapter_response.json()["id"])
        
        # 获取章节列表
        response = await async_client.get(f"/api/v1/persistence/bible/{bible_id}/chapters")
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证响应结构
        assert data["bible_id"] == bible_id
        assert "bible_title" in data
        assert data["total_chapters"] == 3
        assert "chapters" in data
        assert len(data["chapters"]) == 3
        
        # 验证章节按章节号排序
        chapters = data["chapters"]
        for i, chapter in enumerate(chapters):
            assert chapter["chapter_number"] == i + 1
            assert "id" in chapter
            assert "chapter_title" in chapter
            assert "status" in chapter
        
        log_info("测试", "获取故事圣经章节列表API测试通过", 故事圣经ID=bible_id, 章节数量=len(chapters))
    
    async def test_delete_chapter(self, async_client: AsyncClient,
                                 sample_bible_request: Dict[str, Any],
                                 sample_chapter_request: Dict[str, Any]):
        """💾 [数据库] 测试删除章节API"""
        log_debug("测试", "开始测试删除章节API")
        
        # 先创建故事圣经和章节
        bible_response = await async_client.post(
            "/api/v1/persistence/generate-bible",
            json=sample_bible_request
        )
        bible_id = bible_response.json()["id"]
        
        chapter_request = sample_chapter_request.copy()
        chapter_request["story_bible_id"] = bible_id
        
        chapter_response = await async_client.post(
            "/api/v1/persistence/generate-chapter",
            json=chapter_request
        )
        chapter_id = chapter_response.json()["id"]
        
        # 删除章节
        response = await async_client.delete(f"/api/v1/persistence/chapter/{chapter_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert chapter_id in data["message"]
        
        # 验证章节已被删除
        get_response = await async_client.get(f"/api/v1/persistence/chapter/{chapter_id}")
        assert get_response.status_code == 404
        
        log_info("测试", "删除章节API测试通过", 章节ID=chapter_id)
    
    async def test_delete_story_bible(self, async_client: AsyncClient, sample_bible_request: Dict[str, Any]):
        """💾 [数据库] 测试删除故事圣经API"""
        log_debug("测试", "开始测试删除故事圣经API")
        
        # 先创建故事圣经
        create_response = await async_client.post(
            "/api/v1/persistence/generate-bible",
            json=sample_bible_request
        )
        bible_id = create_response.json()["id"]
        
        # 删除故事圣经
        response = await async_client.delete(f"/api/v1/persistence/bible/{bible_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert bible_id in data["message"]
        
        # 验证故事圣经已被删除
        get_response = await async_client.get(f"/api/v1/persistence/bible/{bible_id}")
        assert get_response.status_code == 404
        
        log_info("测试", "删除故事圣经API测试通过", 故事圣经ID=bible_id)
