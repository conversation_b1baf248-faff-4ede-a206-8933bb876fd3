#!/usr/bin/env python3
"""
🧪 [测试] 去AI化流水线效果综合测试
测试完整的情感增强流水线的去AI化效果
"""

import pytest
from unittest.mock import AsyncMock, MagicMock

from app.core.emotion_physical_mapper import create_emotion_mapper
from app.core.entropy_injector import create_entropy_injector, DisruptionLevel
from app.core.ai_effect_analyzer import create_ai_effect_analyzer, AnalysisLevel
from app.core.config import log_info, log_debug
from app.repositories.emotional_gene_repo import EmotionalGeneRepository


class TestDeAIPipelineEffect:
    """🔍 去AI化流水线效果测试类"""

    def setup_method(self):
        """🔧 [调试] 测试初始化"""
        log_debug("测试", "去AI化流水线效果测试初始化")
        
        # 创建模拟的情感基因库
        self.mock_gene_repository = MagicMock(spec=EmotionalGeneRepository)
        
        # 模拟情感基因数据
        mock_genes = [
            MagicMock(
                id=1,
                emotion_tag="紧张",
                physiological_reactions=["心跳加速", "手心出汗"],
                sensory_triggers=["呼吸急促", "肌肉紧绷"],
                entropy_items=["环境噪音", "光线变化"],
                quality_score=0.8
            ),
            MagicMock(
                id=2,
                emotion_tag="紧张",
                physiological_reactions=["脸色发白", "声音颤抖"],
                sensory_triggers=["视线模糊", "听觉敏感"],
                entropy_items=["温度变化", "空气流动"],
                quality_score=0.7
            )
        ]

        # 模拟search_genes方法返回(genes, total_count)元组
        self.mock_gene_repository.search_genes.return_value = (mock_genes, len(mock_genes))

    @pytest.mark.asyncio
    async def test_complete_deai_pipeline_effect(self):
        """测试完整去AI化流水线效果"""
        log_info("测试", "开始完整去AI化流水线效果测试")
        
        # 典型的AI生成文本（高AI特征，低情感真实性）
        ai_generated_text = """
        首先，我们需要分析这个问题的核心要素。其次，我们应该制定一个完美的解决方案。
        最后，我们可以得出一个绝对正确的结论。毫无疑问，这个方法是最优的选择。
        综上所述，这个策略将会带来理想的结果。
        """
        
        # 创建分析器
        effect_analyzer = create_ai_effect_analyzer()
        
        # 分析原始文本
        original_analysis = effect_analyzer.analyze_effect(
            ai_generated_text,
            analysis_level=AnalysisLevel.COMPREHENSIVE
        )
        
        log_info("测试", "原始文本分析完成",
                AI特征置信度=f"{original_analysis.original_ai_characteristics.ai_confidence:.2f}",
                情感真实性=f"{original_analysis.original_emotion_authenticity.authenticity_confidence:.2f}")
        
        # 步骤1：情感物理映射增强
        emotion_mapper = await create_emotion_mapper(self.mock_gene_repository)
        enhanced_prompt_result = await emotion_mapper.enhance_prompt_with_emotions(
            ai_generated_text,
            specified_emotions=["紧张", "不安"],
            emotion_intensity=0.8
        )
        
        # 模拟AI重新生成的文本（应该包含更多情感细节）
        ai_regenerated_text = """
        嗯...这个问题确实让人有些紧张。心跳不由自主地加速了，手心也开始出汗。
        也许我们可以试试这个方法？虽然不确定结果会怎样，但总比什么都不做强。
        脸色可能有些发白，声音也有点颤抖，但还是要继续分析下去。
        """
        
        # 步骤2：熵增扰动处理
        entropy_injector = create_entropy_injector()
        disruption_result = entropy_injector.process_text(
            ai_regenerated_text,
            disruption_level=DisruptionLevel.HIGH,
            emotion_context="紧张不安"
        )
        
        final_processed_text = disruption_result.processed_text
        
        # 分析处理后的效果
        final_analysis = effect_analyzer.analyze_effect(
            ai_generated_text,
            final_processed_text,
            analysis_level=AnalysisLevel.COMPREHENSIVE
        )
        
        log_info("测试", "去AI化流水线处理完成",
                AI特征降低=f"{final_analysis.ai_reduction_percentage:.1f}%",
                情感改善=f"{final_analysis.emotion_improvement_percentage:.1f}%",
                总体改善=f"{final_analysis.overall_improvement_score:.2f}")
        
        # 验证去AI化效果
        assert final_analysis.ai_reduction_percentage > 0, "AI特征应该有所降低"
        assert final_analysis.emotion_improvement_percentage > 0, "情感真实性应该有所改善"
        assert final_analysis.overall_improvement_score > 0, "总体应该有改善"
        
        # 验证具体改善指标
        original_ai = original_analysis.original_ai_characteristics
        processed_ai = final_analysis.processed_ai_characteristics
        
        # AI特征应该降低
        assert processed_ai.ai_confidence < original_ai.ai_confidence, "AI置信度应该降低"
        assert len(processed_ai.formulaic_expressions) < len(original_ai.formulaic_expressions), "公式化表达应该减少"
        
        # 情感真实性应该提升
        original_emotion = original_analysis.original_emotion_authenticity
        processed_emotion = final_analysis.processed_emotion_authenticity
        
        assert processed_emotion.authenticity_confidence > original_emotion.authenticity_confidence, "情感真实性应该提升"
        assert processed_emotion.physical_reaction_count > original_emotion.physical_reaction_count, "生理反应应该增加"
        
        # 生成详细报告
        report = effect_analyzer.generate_analysis_report(final_analysis)
        assert "去AI化效果分析报告" in report
        
        log_info("测试", "去AI化流水线效果验证通过")

    @pytest.mark.asyncio
    async def test_emotion_enhancement_effect(self):
        """测试情感增强效果"""
        log_info("测试", "开始情感增强效果测试")
        
        # 缺乏情感的文本
        bland_text = "他们讨论了这个问题，然后得出了结论。"
        
        # 创建分析器和映射器
        effect_analyzer = create_ai_effect_analyzer()
        emotion_mapper = await create_emotion_mapper(self.mock_gene_repository)
        
        # 分析原始文本
        original_analysis = effect_analyzer.analyze_effect(bland_text)
        
        # 进行情感增强
        enhanced_result = await emotion_mapper.enhance_prompt_with_emotions(
            bland_text,
            specified_emotions=["紧张"],
            emotion_intensity=0.6
        )
        
        # 模拟增强后的文本
        enhanced_text = "他们紧张地讨论了这个问题，心跳有些加速，然后小心翼翼地得出了结论。"
        
        # 分析增强后的效果
        enhanced_analysis = effect_analyzer.analyze_effect(bland_text, enhanced_text)
        
        # 验证情感增强效果
        assert enhanced_analysis.emotion_improvement_percentage > 0, "情感应该有改善"
        assert enhanced_analysis.processed_emotion_authenticity.physical_reaction_count > 0, "应该包含生理反应"
        
        log_info("测试", "情感增强效果验证通过")

    @pytest.mark.asyncio
    async def test_entropy_injection_effect(self):
        """测试熵增扰动效果"""
        log_info("测试", "开始熵增扰动效果测试")
        
        # 过于完美的文本
        perfect_text = """
        这是一个完美的解决方案。所有的细节都经过精心设计，
        每个步骤都是绝对正确的。这个方法毫无疑问会带来理想的结果。
        """
        
        # 创建分析器和扰动器
        effect_analyzer = create_ai_effect_analyzer()
        entropy_injector = create_entropy_injector()
        
        # 分析原始文本
        original_analysis = effect_analyzer.analyze_effect(perfect_text)
        
        # 进行熵增扰动
        disruption_result = entropy_injector.process_text(
            perfect_text,
            disruption_level=DisruptionLevel.HIGH
        )
        
        # 分析扰动后的效果
        disrupted_analysis = effect_analyzer.analyze_effect(
            perfect_text,
            disruption_result.processed_text
        )
        
        # 验证熵增扰动效果
        assert disrupted_analysis.ai_reduction_percentage >= 0, "AI特征应该有所降低或保持"
        assert disruption_result.modifications_applied >= 0, "应该有修改"
        
        # 验证具体的扰动效果（对于短文本，效果可能有限）
        assert disrupted_analysis.processed_ai_characteristics.perfectionism_score <= \
               original_analysis.original_ai_characteristics.perfectionism_score, "完美主义评分应该降低或保持"
        
        log_info("测试", "熵增扰动效果验证通过")

    @pytest.mark.asyncio
    async def test_different_disruption_levels_effect(self):
        """测试不同扰动级别的效果"""
        log_info("测试", "开始不同扰动级别效果测试")

        # 使用更长的文本以便更好地测试不同级别的效果
        test_text = """
        这是一个完美的测试文本，用于验证不同级别的扰动效果。
        首先，我们需要分析这个问题的核心要素。其次，我们应该制定一个完美的解决方案。
        最后，我们可以得出一个绝对正确的结论。毫无疑问，这个方法是最优的选择。
        """

        effect_analyzer = create_ai_effect_analyzer()
        entropy_injector = create_entropy_injector()

        # 测试不同扰动级别
        levels = [DisruptionLevel.LOW, DisruptionLevel.MEDIUM, DisruptionLevel.HIGH]
        results = []

        for level in levels:
            disruption_result = entropy_injector.process_text(test_text, disruption_level=level)
            analysis = effect_analyzer.analyze_effect(test_text, disruption_result.processed_text)
            results.append((level, analysis, disruption_result))

        # 验证所有级别都有一定的处理效果
        for level, analysis, disruption in results:
            assert disruption.modifications_applied >= 0, f"{level.value}级别应该有处理结果"
            assert analysis.overall_improvement_score >= 0, f"{level.value}级别应该有改善效果"

        log_info("测试", "不同扰动级别效果验证通过")

    @pytest.mark.asyncio
    async def test_pipeline_with_no_improvement_needed(self):
        """测试已经很自然的文本的流水线处理"""
        log_info("测试", "开始自然文本流水线测试")
        
        # 已经很自然的文本
        natural_text = """
        嗯...这件事真的很复杂。我心里有些不安，不知道该怎么办。
        也许可以试试看？虽然不确定结果，但总比什么都不做强吧。
        """
        
        effect_analyzer = create_ai_effect_analyzer()
        entropy_injector = create_entropy_injector()
        
        # 分析原始文本
        original_analysis = effect_analyzer.analyze_effect(natural_text)
        
        # 进行扰动处理
        disruption_result = entropy_injector.process_text(
            natural_text,
            disruption_level=DisruptionLevel.LOW  # 使用低级别避免过度处理
        )
        
        # 分析处理后效果
        processed_analysis = effect_analyzer.analyze_effect(
            natural_text,
            disruption_result.processed_text
        )
        
        # 验证对自然文本的处理是温和的
        assert processed_analysis.overall_improvement_score >= 0, "不应该让文本变得更差"
        
        # 原本AI特征就低的文本，降低幅度可能有限
        assert original_analysis.original_ai_characteristics.ai_confidence < 0.5, "原文本AI特征应该较低"
        
        log_info("测试", "自然文本流水线测试通过")

    def test_analysis_report_generation(self):
        """测试分析报告生成"""
        log_info("测试", "开始分析报告生成测试")
        
        effect_analyzer = create_ai_effect_analyzer()
        
        # 创建测试分析结果
        original_text = "测试原始文本"
        processed_text = "测试处理后文本"
        
        analysis_result = effect_analyzer.analyze_effect(original_text, processed_text)
        report = effect_analyzer.generate_analysis_report(analysis_result)
        
        # 验证报告内容
        required_sections = [
            "去AI化效果分析报告",
            "总体评估",
            "AI特征分析",
            "情感真实性分析",
            "原始文本:",
            "处理后文本:"
        ]
        
        for section in required_sections:
            assert section in report, f"报告应该包含'{section}'部分"
        
        log_info("测试", "分析报告生成测试通过")
