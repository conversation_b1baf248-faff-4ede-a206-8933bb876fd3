"""
📝 [生成] 故事圣经数据模型
定义故事圣经和章节的数据库表结构
"""

from datetime import datetime
from typing import Optional, List
from sqlalchemy import String, Text, DateTime, Integer, Float, Enum as SQLEnum, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship
import enum

from app.core.base import Base
from app.schemas.generation import AIProvider, StoryGenre, GenerationStatus


class StoryBible(Base):
    """
    📝 [生成] 故事圣经数据模型
    
    存储小说的基础信息和生成的故事圣经内容
    """
    __tablename__ = "story_bibles"
    
    # 主键
    id: Mapped[str] = mapped_column(String(50), primary_key=True, comment="故事圣经唯一标识")
    
    # 基础信息
    title: Mapped[str] = mapped_column(String(200), nullable=False, comment="小说标题")
    genre: Mapped[StoryGenre] = mapped_column(SQLEnum(StoryGenre), nullable=False, comment="小说类型")
    theme: Mapped[str] = mapped_column(Text, nullable=False, comment="小说主题")
    protagonist: Mapped[str] = mapped_column(Text, nullable=False, comment="主角设定")
    setting: Mapped[str] = mapped_column(Text, nullable=False, comment="故事背景")
    plot_outline: Mapped[str] = mapped_column(Text, nullable=False, comment="情节大纲")
    
    # 可选信息
    target_audience: Mapped[Optional[str]] = mapped_column(String(500), comment="目标读者")
    writing_style: Mapped[Optional[str]] = mapped_column(String(500), comment="写作风格")
    
    # 生成配置
    ai_provider: Mapped[AIProvider] = mapped_column(SQLEnum(AIProvider), nullable=False, comment="AI提供商")
    temperature: Mapped[float] = mapped_column(Float, default=0.8, comment="生成温度参数")
    max_tokens: Mapped[int] = mapped_column(Integer, default=3000, comment="最大生成token数")
    
    # 生成状态和结果
    status: Mapped[GenerationStatus] = mapped_column(
        SQLEnum(GenerationStatus), 
        default=GenerationStatus.PENDING, 
        comment="生成状态"
    )
    generated_content: Mapped[Optional[str]] = mapped_column(Text, comment="生成的故事圣经内容")
    error_message: Mapped[Optional[str]] = mapped_column(Text, comment="错误信息")
    
    # 生成统计信息
    content_length: Mapped[Optional[int]] = mapped_column(Integer, comment="内容长度")
    generation_time: Mapped[Optional[float]] = mapped_column(Float, comment="生成耗时（秒）")
    token_usage: Mapped[Optional[int]] = mapped_column(Integer, comment="使用的token数量")
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        nullable=False, 
        comment="创建时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False, 
        comment="更新时间"
    )
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime, comment="完成时间")
    
    # 关系映射
    chapters: Mapped[List["Chapter"]] = relationship(
        "Chapter", 
        back_populates="story_bible",
        cascade="all, delete-orphan",
        order_by="Chapter.chapter_number"
    )
    
    def __repr__(self) -> str:
        return f"<StoryBible(id='{self.id}', title='{self.title}', status='{self.status}')>"


class Chapter(Base):
    """
    📝 [生成] 章节数据模型
    
    存储小说章节的信息和生成内容
    """
    __tablename__ = "chapters"
    
    # 主键
    id: Mapped[str] = mapped_column(String(50), primary_key=True, comment="章节唯一标识")
    
    # 外键关联
    story_bible_id: Mapped[str] = mapped_column(
        String(50), 
        ForeignKey("story_bibles.id", ondelete="CASCADE"), 
        nullable=False, 
        comment="关联的故事圣经ID"
    )
    
    # 章节基础信息
    chapter_number: Mapped[int] = mapped_column(Integer, nullable=False, comment="章节号")
    chapter_title: Mapped[str] = mapped_column(String(200), nullable=False, comment="章节标题")
    chapter_outline: Mapped[str] = mapped_column(Text, nullable=False, comment="章节大纲")
    
    # 可选信息
    previous_chapter_summary: Mapped[Optional[str]] = mapped_column(Text, comment="前章摘要")
    character_development: Mapped[Optional[str]] = mapped_column(Text, comment="角色发展要求")
    plot_requirements: Mapped[Optional[str]] = mapped_column(Text, comment="情节要求")
    target_word_count: Mapped[int] = mapped_column(Integer, default=2000, comment="目标字数")
    
    # 生成配置
    ai_provider: Mapped[AIProvider] = mapped_column(SQLEnum(AIProvider), nullable=False, comment="AI提供商")
    temperature: Mapped[float] = mapped_column(Float, default=0.8, comment="生成温度参数")
    max_tokens: Mapped[int] = mapped_column(Integer, default=4000, comment="最大生成token数")
    
    # 生成状态和结果
    status: Mapped[GenerationStatus] = mapped_column(
        SQLEnum(GenerationStatus), 
        default=GenerationStatus.PENDING, 
        comment="生成状态"
    )
    generated_content: Mapped[Optional[str]] = mapped_column(Text, comment="生成的章节内容")
    error_message: Mapped[Optional[str]] = mapped_column(Text, comment="错误信息")
    
    # 生成统计信息
    content_length: Mapped[Optional[int]] = mapped_column(Integer, comment="内容长度")
    actual_word_count: Mapped[Optional[int]] = mapped_column(Integer, comment="实际字数")
    generation_time: Mapped[Optional[float]] = mapped_column(Float, comment="生成耗时（秒）")
    token_usage: Mapped[Optional[int]] = mapped_column(Integer, comment="使用的token数量")
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        nullable=False, 
        comment="创建时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False, 
        comment="更新时间"
    )
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime, comment="完成时间")
    
    # 关系映射
    story_bible: Mapped["StoryBible"] = relationship("StoryBible", back_populates="chapters")
    
    def __repr__(self) -> str:
        return f"<Chapter(id='{self.id}', chapter_number={self.chapter_number}, title='{self.chapter_title}', status='{self.status}')>"
