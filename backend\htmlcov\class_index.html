<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">90%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-02 12:57 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698___init___py.html">app\__init__.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2___init___py.html">app\core\__init__.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t12">app\core\config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t12"><data value='Settings'>Settings</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html">app\core\config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>47</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="44 47">94%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d___init___py.html">app\models\__init__.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_84e85958b078cc7a___init___py.html">app\routers\__init__.py</a></td>
                <td class="name left"><a href="z_84e85958b078cc7a___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_84e85958b078cc7a_generation_py.html">app\routers\generation.py</a></td>
                <td class="name left"><a href="z_84e85958b078cc7a_generation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>133</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="109 133">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174___init___py.html">app\schemas\__init__.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t11">app\schemas\generation.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t11"><data value='AIProvider'>AIProvider</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t17">app\schemas\generation.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t17"><data value='GenerationStatus'>GenerationStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t25">app\schemas\generation.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t25"><data value='StoryGenre'>StoryGenre</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t37">app\schemas\generation.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t37"><data value='StoryBibleRequest'>StoryBibleRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t54">app\schemas\generation.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t54"><data value='ChapterGenerationRequest'>ChapterGenerationRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t71">app\schemas\generation.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t71"><data value='GenerationTaskBase'>GenerationTaskBase</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t82">app\schemas\generation.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t82"><data value='StoryBibleResponse'>StoryBibleResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t100">app\schemas\generation.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t100"><data value='ChapterResponse'>ChapterResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t117">app\schemas\generation.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t117"><data value='GenerationProgress'>GenerationProgress</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t126">app\schemas\generation.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t126"><data value='GenerationError'>GenerationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t135">app\schemas\generation.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t135"><data value='TaskStatusResponse'>TaskStatusResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t144">app\schemas\generation.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t144"><data value='GenerationListResponse'>GenerationListResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t152">app\schemas\generation.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t152"><data value='HealthCheckResponse'>HealthCheckResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html">app\schemas\generation.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>99</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="99 99">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70___init___py.html">app\services\__init__.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_zhipu_client_py.html#t15">app\services\zhipu_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_zhipu_client_py.html#t15"><data value='ChatMessage'>ChatMessage</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_zhipu_client_py.html#t21">app\services\zhipu_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_zhipu_client_py.html#t21"><data value='ChatCompletionRequest'>ChatCompletionRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_zhipu_client_py.html#t32">app\services\zhipu_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_zhipu_client_py.html#t32"><data value='ChatCompletionResponse'>ChatCompletionResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_zhipu_client_py.html#t42">app\services\zhipu_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_zhipu_client_py.html#t42"><data value='ZhipuAIError'>ZhipuAIError</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_zhipu_client_py.html#t51">app\services\zhipu_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_zhipu_client_py.html#t51"><data value='ZhipuClient'>ZhipuClient</data></a></td>
                <td>89</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="73 89">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_zhipu_client_py.html">app\services\zhipu_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_zhipu_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>46</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="46 46">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1___init___py.html">app\utils\__init__.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>419</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="376 419">90%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-02 12:57 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
