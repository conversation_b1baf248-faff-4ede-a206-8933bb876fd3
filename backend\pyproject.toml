[project]
name = "wenxin-novel-backend"
version = "0.1.0"
description = "文心小说后端服务系统 - FastAPI后端服务，支持智谱AI和Kimi AI的多AI服务架构"
authors = [
    {name = "AI小速写团队", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.11"

# 项目直接依赖
dependencies = [
    "fastapi>=0.110.0",
    "uvicorn[standard]>=0.25.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "python-dotenv>=1.0.0",
    "httpx>=0.26.0",
    "tenacity>=8.2.0",
    "python-multipart>=0.0.6",
    "structlog>=23.2.0",
    "sqlalchemy[asyncio]>=2.0.25",
    "alembic>=1.13.0",
    "aiosqlite>=0.19.0",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "jieba>=0.42.1",
]

# 开发依赖
[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-httpx>=0.26.0",
    "httpx>=0.26.0",
    "pytest-cov>=4.1.0",
    "black>=23.12.0",
    "isort>=5.13.0",
    "flake8>=6.1.0",
    "mypy>=1.8.0",
    "pre-commit>=3.6.0",
]

test = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-httpx>=0.26.0",
    "httpx>=0.26.0",
    "pytest-cov>=4.1.0",
]

# 构建系统配置
[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

# 构建目标配置
[tool.hatch.build.targets.wheel]
packages = ["app"]

# 项目URL
[project.urls]
Homepage = "https://github.com/ai-novel/wenxin-novel-backend"
Documentation = "https://github.com/ai-novel/wenxin-novel-backend/docs"
Repository = "https://github.com/ai-novel/wenxin-novel-backend"
"Bug Tracker" = "https://github.com/ai-novel/wenxin-novel-backend/issues"

# 工具配置
[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "-ra",
    "--strict-markers",
    "--strict-config",
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
asyncio_mode = "auto"

[tool.coverage.run]
source = ["app"]
omit = [
    "tests/*",
    "*/migrations/*",
    "*/venv/*",
    "*/.venv/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "if TYPE_CHECKING:",
]

[tool.black]
line-length = 100
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | migrations
)/
'''

[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
skip_glob = ["*/migrations/*"]

[tool.mypy]
python_version = "3.11"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_return_any = true
strict_equality = true
exclude = [
    "migrations/",
    "tests/",
]

[[tool.mypy.overrides]]
module = [
    "tenacity.*",
    "structlog.*",
]
ignore_missing_imports = true
