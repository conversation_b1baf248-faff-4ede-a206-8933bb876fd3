"""
💾 [数据库] 故事圣经仓库测试
测试故事圣经数据仓库的CRUD操作和业务逻辑
"""

import pytest
import tempfile
from pathlib import Path
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker

from app.core.database import Base
from app.repositories.bible_repo import StoryBibleRepository
from app.schemas.generation import StoryGenre, AIProvider, GenerationStatus
from app.core.config import log_info, log_debug


class TestStoryBibleRepository:
    """💾 [数据库] 故事圣经仓库测试类"""
    
    @pytest.fixture
    async def test_session(self):
        """创建测试数据库会话"""
        # 创建临时数据库
        temp_dir = tempfile.mkdtemp()
        temp_db_path = Path(temp_dir) / "test_bible_repo.db"
        db_url = f"sqlite+aiosqlite:///{temp_db_path}"
        
        log_debug("数据库", "创建故事圣经仓库测试数据库", 路径=str(temp_db_path))
        
        # 创建引擎和会话
        engine = create_async_engine(db_url)
        async_session_maker = async_sessionmaker(bind=engine, class_=AsyncSession)
        
        # 创建表结构
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        # 提供会话
        async with async_session_maker() as session:
            yield session
        
        # 清理
        await engine.dispose()
        try:
            if temp_db_path.exists():
                temp_db_path.unlink()
        except PermissionError:
            pass
    
    @pytest.fixture
    def sample_bible_data(self):
        """示例故事圣经数据"""
        return {
            "id": "test-bible-001",
            "title": "测试小说",
            "genre": StoryGenre.FANTASY,
            "theme": "魔法与冒险的世界",
            "protagonist": "年轻的魔法师艾莉丝",
            "setting": "中世纪魔法王国",
            "plot_outline": "主角在魔法学院学习，最终拯救王国",
            "target_audience": "青少年读者",
            "writing_style": "轻松幽默",
            "ai_provider": AIProvider.ZHIPU,
            "temperature": 0.8,
            "max_tokens": 3000,
            "status": GenerationStatus.PENDING
        }
    
    async def test_create_bible(self, test_session, sample_bible_data):
        """💾 [数据库] 测试创建故事圣经"""
        repo = StoryBibleRepository(test_session)
        
        # 创建故事圣经
        bible = await repo.create_bible(sample_bible_data)
        
        # 验证创建结果
        assert bible.id == sample_bible_data["id"]
        assert bible.title == sample_bible_data["title"]
        assert bible.genre == sample_bible_data["genre"]
        assert bible.status == GenerationStatus.PENDING
        assert bible.created_at is not None
        
        log_info("数据库", "故事圣经创建测试通过", 故事圣经ID=bible.id)
    
    async def test_get_bible_by_id(self, test_session, sample_bible_data):
        """💾 [数据库] 测试根据ID获取故事圣经"""
        repo = StoryBibleRepository(test_session)
        
        # 先创建故事圣经
        created_bible = await repo.create_bible(sample_bible_data)
        
        # 根据ID获取
        retrieved_bible = await repo.get_bible_by_id(created_bible.id)
        
        # 验证获取结果
        assert retrieved_bible is not None
        assert retrieved_bible.id == created_bible.id
        assert retrieved_bible.title == created_bible.title
        
        # 测试不存在的ID
        non_existent = await repo.get_bible_by_id("non-existent-id")
        assert non_existent is None
        
        log_info("数据库", "故事圣经查询测试通过")
    
    async def test_update_bible_status(self, test_session, sample_bible_data):
        """💾 [数据库] 测试更新故事圣经状态"""
        repo = StoryBibleRepository(test_session)
        
        # 创建故事圣经
        bible = await repo.create_bible(sample_bible_data)
        
        # 更新状态为生成中
        success = await repo.update_bible_status(
            bible.id, 
            GenerationStatus.GENERATING
        )
        assert success is True
        
        # 验证状态更新 - 重新查询以避免懒加载问题
        await test_session.refresh(bible)
        assert bible.status == GenerationStatus.GENERATING
        
        # 更新为完成状态，包含生成内容
        generated_content = "这是生成的故事圣经内容..."
        success = await repo.update_bible_status(
            bible.id,
            GenerationStatus.COMPLETED,
            generated_content=generated_content,
            generation_time=5.2,
            token_usage=1500
        )
        assert success is True
        
        # 验证完成状态更新 - 重新查询以避免懒加载问题
        await test_session.refresh(bible)
        assert bible.status == GenerationStatus.COMPLETED
        assert bible.generated_content == generated_content
        assert bible.content_length == len(generated_content)
        assert bible.generation_time == 5.2
        assert bible.token_usage == 1500
        assert bible.completed_at is not None
        
        # 测试更新不存在的记录
        success = await repo.update_bible_status(
            "non-existent-id", 
            GenerationStatus.FAILED
        )
        assert success is False
        
        log_info("数据库", "故事圣经状态更新测试通过")
    
    async def test_list_bibles(self, test_session):
        """💾 [数据库] 测试获取故事圣经列表"""
        repo = StoryBibleRepository(test_session)
        
        # 创建多个故事圣经
        bible_data_list = [
            {
                "id": f"test-bible-{i:03d}",
                "title": f"测试小说{i}",
                "genre": StoryGenre.FANTASY if i % 2 == 0 else StoryGenre.ROMANCE,
                "theme": f"主题{i}",
                "protagonist": f"主角{i}",
                "setting": f"背景{i}",
                "plot_outline": f"大纲{i}",
                "ai_provider": AIProvider.ZHIPU,
                "temperature": 0.8,
                "max_tokens": 3000,
                "status": GenerationStatus.COMPLETED if i % 2 == 0 else GenerationStatus.PENDING
            }
            for i in range(1, 6)
        ]
        
        for bible_data in bible_data_list:
            await repo.create_bible(bible_data)
        
        # 测试获取所有故事圣经
        all_bibles = await repo.list_bibles(limit=10)
        assert len(all_bibles) == 5
        
        # 测试分页
        page1 = await repo.list_bibles(limit=2, offset=0)
        page2 = await repo.list_bibles(limit=2, offset=2)
        assert len(page1) == 2
        assert len(page2) == 2
        assert page1[0].id != page2[0].id
        
        # 测试状态过滤
        pending_bibles = await repo.list_bibles(status_filter=GenerationStatus.PENDING)
        completed_bibles = await repo.list_bibles(status_filter=GenerationStatus.COMPLETED)
        assert len(pending_bibles) == 3  # 索引1,3,5 (奇数索引为PENDING)
        assert len(completed_bibles) == 2  # 索引2,4 (偶数索引为COMPLETED)
        
        # 测试类型过滤
        fantasy_bibles = await repo.list_bibles(genre_filter=StoryGenre.FANTASY)
        romance_bibles = await repo.list_bibles(genre_filter=StoryGenre.ROMANCE)
        assert len(fantasy_bibles) == 2  # 索引2,4 (偶数索引为FANTASY)
        assert len(romance_bibles) == 3  # 索引1,3,5 (奇数索引为ROMANCE)
        
        log_info("数据库", "故事圣经列表查询测试通过")
    
    async def test_delete_bible(self, test_session, sample_bible_data):
        """💾 [数据库] 测试删除故事圣经"""
        repo = StoryBibleRepository(test_session)
        
        # 创建故事圣经
        bible = await repo.create_bible(sample_bible_data)
        
        # 验证存在
        retrieved = await repo.get_bible_by_id(bible.id)
        assert retrieved is not None
        
        # 删除故事圣经
        success = await repo.delete_bible(bible.id)
        assert success is True
        
        # 验证已删除
        deleted = await repo.get_bible_by_id(bible.id)
        assert deleted is None
        
        # 测试删除不存在的记录
        success = await repo.delete_bible("non-existent-id")
        assert success is False
        
        log_info("数据库", "故事圣经删除测试通过")
    
    async def test_get_bible_with_chapters(self, test_session, sample_bible_data):
        """💾 [数据库] 测试获取故事圣经及其章节"""
        repo = StoryBibleRepository(test_session)
        
        # 创建故事圣经
        bible = await repo.create_bible(sample_bible_data)
        
        # 获取包含章节的故事圣经（此时应该没有章节）
        bible_with_chapters = await repo.get_bible_with_chapters(bible.id)
        
        # 验证结果
        assert bible_with_chapters is not None
        assert bible_with_chapters.id == bible.id
        assert len(bible_with_chapters.chapters) == 0
        
        # 测试不存在的ID
        non_existent = await repo.get_bible_with_chapters("non-existent-id")
        assert non_existent is None
        
        log_info("数据库", "故事圣经及章节查询测试通过")
