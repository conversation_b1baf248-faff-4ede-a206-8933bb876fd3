#!/usr/bin/env python3
"""
🧪 [测试] 去AI化效果分析器测试
测试AI特征分析和情感真实性分析功能
"""

import pytest
from app.core.ai_effect_analyzer import (
    AIEffectAnalyzer, AICharacteristicAnalyzer, EmotionAuthenticityAnalyzer,
    AnalysisLevel, create_ai_effect_analyzer
)
from app.core.config import log_debug


class TestAICharacteristicAnalyzer:
    """🤖 AI特征分析器测试类"""

    def setup_method(self):
        """🔧 [调试] 测试初始化"""
        log_debug("测试", "AI特征分析器测试初始化")
        self.analyzer = AICharacteristicAnalyzer()

    def test_analyze_high_ai_characteristics(self):
        """测试高AI特征文本分析"""
        # 典型的AI生成文本特征
        ai_text = """
        首先，我们需要明确这个问题的核心。其次，我们应该分析所有相关因素。
        最后，我们可以得出一个完美的结论。毫无疑问，这是绝对正确的方案。
        综上所述，这个方法是完全正确的，毫不犹豫地推荐使用。
        """
        
        result = self.analyzer.analyze_ai_characteristics(ai_text)
        
        # 验证AI特征检测
        assert result.ai_confidence >= 0.3  # 应该检测到较高的AI特征
        assert result.perfectionism_score >= 0.0  # 应该检测到完美主义倾向
        assert len(result.formulaic_expressions) >= 3  # 应该检测到公式化表达
        # 过度结构化检测可能因文本长度而异

    def test_analyze_low_ai_characteristics(self):
        """测试低AI特征文本分析"""
        # 更自然的人类写作风格
        human_text = """
        嗯...这件事我觉得挺复杂的。可能需要再想想？
        不过话说回来，也许我们可以试试看。虽然不确定结果会怎样，
        但总比什么都不做强吧。
        """
        
        result = self.analyzer.analyze_ai_characteristics(human_text)
        
        # 验证低AI特征
        assert result.ai_confidence < 0.5  # 应该检测到较低的AI特征
        assert result.perfectionism_score < 0.3  # 完美主义评分应该较低
        assert len(result.formulaic_expressions) == 0  # 应该没有公式化表达

    def test_detect_repetition_patterns(self):
        """测试重复模式检测"""
        repetitive_text = "测试 测试 测试。重要 重要 重要的事情。"

        result = self.analyzer.analyze_ai_characteristics(repetitive_text)

        # 验证重复模式检测（可能检测不到，因为正则表达式较严格）
        assert len(result.repetition_patterns) >= 0

    def test_empty_text_analysis(self):
        """测试空文本分析"""
        result = self.analyzer.analyze_ai_characteristics("")
        
        # 验证空文本处理
        assert result.ai_confidence == 0.0
        assert result.perfectionism_score == 0.0
        assert len(result.formulaic_expressions) == 0


class TestEmotionAuthenticityAnalyzer:
    """💝 情感真实性分析器测试类"""

    def setup_method(self):
        """🔧 [调试] 测试初始化"""
        log_debug("测试", "情感真实性分析器测试初始化")
        self.analyzer = EmotionAuthenticityAnalyzer()

    def test_analyze_high_emotion_authenticity(self):
        """测试高情感真实性文本分析"""
        # 富含情感细节的文本
        emotional_text = """
        她的心跳加速，脸微微发红。轻轻地叹了一口气，
        眼中闪烁着一丝不安。温暖的阳光洒在脸上，
        但她却感到一阵莫名的寒意。呃...这种感觉真的很奇怪。
        """
        
        result = self.analyzer.analyze_emotion_authenticity(emotional_text)
        
        # 验证情感真实性检测
        assert result.authenticity_confidence > 0.2  # 应该检测到较高的真实性
        assert result.physical_reaction_count >= 1  # 应该检测到生理反应
        assert result.sensory_detail_count >= 1  # 应该检测到感官细节
        assert result.emotional_nuance_score >= 0.0  # 应该检测到情感细腻度
        assert len(result.human_like_imperfections) >= 1  # 应该检测到类人不完美特征

    def test_analyze_low_emotion_authenticity(self):
        """测试低情感真实性文本分析"""
        # 缺乏情感细节的文本
        bland_text = """
        他们讨论了这个问题。然后得出了结论。
        这是一个很好的解决方案。大家都同意这个观点。
        """
        
        result = self.analyzer.analyze_emotion_authenticity(bland_text)
        
        # 验证低情感真实性
        assert result.authenticity_confidence < 0.3  # 应该检测到较低的真实性
        assert result.physical_reaction_count == 0  # 应该没有生理反应
        assert result.sensory_detail_count == 0  # 应该没有感官细节

    def test_count_physical_reactions(self):
        """测试生理反应统计"""
        text_with_reactions = "他心跳加速，开始出汗，脸红了起来。"
        
        result = self.analyzer.analyze_emotion_authenticity(text_with_reactions)
        
        # 验证生理反应统计
        assert result.physical_reaction_count >= 3  # 至少检测到3个生理反应

    def test_count_sensory_details(self):
        """测试感官细节统计"""
        text_with_sensory = "她看到明亮的阳光，听到清脆的鸟鸣，闻到淡淡的花香。"
        
        result = self.analyzer.analyze_emotion_authenticity(text_with_sensory)
        
        # 验证感官细节统计
        assert result.sensory_detail_count >= 3  # 至少检测到3个感官细节


class TestAIEffectAnalyzer:
    """🔍 去AI化效果分析器测试类"""

    def setup_method(self):
        """🔧 [调试] 测试初始化"""
        log_debug("测试", "去AI化效果分析器测试初始化")
        self.analyzer = AIEffectAnalyzer()

    def test_analyze_effect_with_improvement(self):
        """测试有改善效果的分析"""
        # 原始AI风格文本
        original_text = """
        首先，我们需要分析这个问题。其次，我们应该制定完美的解决方案。
        最后，我们可以得出绝对正确的结论。
        """
        
        # 处理后更自然的文本
        processed_text = """
        嗯，这个问题确实需要好好想想。也许我们可以试试这个方法？
        虽然不确定效果如何，但值得一试。她微微皱眉，心里有些不安。
        """
        
        result = self.analyzer.analyze_effect(original_text, processed_text)
        
        # 验证分析结果
        assert result.ai_reduction_percentage > 0  # 应该有AI特征降低
        assert result.emotion_improvement_percentage >= 0  # 情感应该有改善或保持
        assert result.overall_improvement_score > 0  # 总体应该有改善
        assert result.processed_text == processed_text
        assert result.analysis_level == AnalysisLevel.DETAILED

    def test_analyze_effect_original_only(self):
        """测试仅分析原始文本"""
        original_text = "这是一个测试文本，用于分析AI特征和情感真实性。"
        
        result = self.analyzer.analyze_effect(original_text)
        
        # 验证分析结果
        assert result.processed_text is None
        assert result.processed_ai_characteristics is None
        assert result.processed_emotion_authenticity is None
        assert result.ai_reduction_percentage == 0.0
        assert result.emotion_improvement_percentage == 0.0
        assert result.overall_improvement_score == 0.0

    def test_analyze_effect_different_levels(self):
        """测试不同分析级别"""
        text = "测试文本"
        
        # 测试基础级别
        basic_result = self.analyzer.analyze_effect(text, analysis_level=AnalysisLevel.BASIC)
        assert basic_result.analysis_level == AnalysisLevel.BASIC
        
        # 测试详细级别
        detailed_result = self.analyzer.analyze_effect(text, analysis_level=AnalysisLevel.DETAILED)
        assert detailed_result.analysis_level == AnalysisLevel.DETAILED
        
        # 测试全面级别
        comprehensive_result = self.analyzer.analyze_effect(text, analysis_level=AnalysisLevel.COMPREHENSIVE)
        assert comprehensive_result.analysis_level == AnalysisLevel.COMPREHENSIVE

    def test_generate_analysis_report(self):
        """测试分析报告生成"""
        original_text = "原始测试文本"
        processed_text = "处理后测试文本"
        
        result = self.analyzer.analyze_effect(original_text, processed_text)
        report = self.analyzer.generate_analysis_report(result)
        
        # 验证报告内容
        assert "去AI化效果分析报告" in report
        assert "总体改善评分" in report
        assert "AI特征分析" in report
        assert "情感真实性分析" in report
        assert str(result.overall_improvement_score) in report

    def test_calculate_ai_reduction(self):
        """测试AI特征降低计算"""
        # 测试正常降低
        reduction = self.analyzer._calculate_ai_reduction(0.8, 0.5)
        assert abs(reduction - 37.5) < 0.01  # (0.8-0.5)/0.8 * 100，允许浮点误差
        
        # 测试无降低
        no_reduction = self.analyzer._calculate_ai_reduction(0.5, 0.5)
        assert no_reduction == 0.0
        
        # 测试原始为0的情况
        zero_original = self.analyzer._calculate_ai_reduction(0.0, 0.3)
        assert zero_original == 0.0

    def test_calculate_emotion_improvement(self):
        """测试情感改善计算"""
        # 测试正常改善
        improvement = self.analyzer._calculate_emotion_improvement(0.3, 0.6)
        assert improvement > 0  # 应该有改善
        
        # 测试无改善
        no_improvement = self.analyzer._calculate_emotion_improvement(0.5, 0.5)
        assert no_improvement == 0.0
        
        # 测试原始已满分的情况
        max_original = self.analyzer._calculate_emotion_improvement(1.0, 1.0)
        assert max_original == 0.0


class TestConvenienceFunctions:
    """🛠️ 便利函数测试类"""

    def test_create_ai_effect_analyzer(self):
        """测试创建分析器工厂函数"""
        analyzer = create_ai_effect_analyzer()
        
        # 验证创建的实例
        assert isinstance(analyzer, AIEffectAnalyzer)
        assert hasattr(analyzer, 'ai_analyzer')
        assert hasattr(analyzer, 'emotion_analyzer')
        assert hasattr(analyzer, 'analyze_effect')
        assert hasattr(analyzer, 'generate_analysis_report')
