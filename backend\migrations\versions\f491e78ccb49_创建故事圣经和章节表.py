"""创建故事圣经和章节表

Revision ID: f491e78ccb49
Revises: 
Create Date: 2025-08-02 13:10:40.319737

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f491e78ccb49'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('story_bibles',
    sa.Column('id', sa.String(length=50), nullable=False, comment='故事圣经唯一标识'),
    sa.Column('title', sa.String(length=200), nullable=False, comment='小说标题'),
    sa.Column('genre', sa.Enum('ROMANCE', 'FANTASY', 'URBAN', 'HISTORICAL', 'MYSTERY', 'SCIFI', 'MARTIAL_ARTS', 'THRILLER', name='storygenre'), nullable=False, comment='小说类型'),
    sa.Column('theme', sa.Text(), nullable=False, comment='小说主题'),
    sa.Column('protagonist', sa.Text(), nullable=False, comment='主角设定'),
    sa.Column('setting', sa.Text(), nullable=False, comment='故事背景'),
    sa.Column('plot_outline', sa.Text(), nullable=False, comment='情节大纲'),
    sa.Column('target_audience', sa.String(length=500), nullable=True, comment='目标读者'),
    sa.Column('writing_style', sa.String(length=500), nullable=True, comment='写作风格'),
    sa.Column('ai_provider', sa.Enum('ZHIPU', 'KIMI', name='aiprovider'), nullable=False, comment='AI提供商'),
    sa.Column('temperature', sa.Float(), nullable=False, comment='生成温度参数'),
    sa.Column('max_tokens', sa.Integer(), nullable=False, comment='最大生成token数'),
    sa.Column('status', sa.Enum('PENDING', 'GENERATING', 'COMPLETED', 'FAILED', name='generationstatus'), nullable=False, comment='生成状态'),
    sa.Column('generated_content', sa.Text(), nullable=True, comment='生成的故事圣经内容'),
    sa.Column('error_message', sa.Text(), nullable=True, comment='错误信息'),
    sa.Column('content_length', sa.Integer(), nullable=True, comment='内容长度'),
    sa.Column('generation_time', sa.Float(), nullable=True, comment='生成耗时（秒）'),
    sa.Column('token_usage', sa.Integer(), nullable=True, comment='使用的token数量'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.Column('completed_at', sa.DateTime(), nullable=True, comment='完成时间'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('chapters',
    sa.Column('id', sa.String(length=50), nullable=False, comment='章节唯一标识'),
    sa.Column('story_bible_id', sa.String(length=50), nullable=False, comment='关联的故事圣经ID'),
    sa.Column('chapter_number', sa.Integer(), nullable=False, comment='章节号'),
    sa.Column('chapter_title', sa.String(length=200), nullable=False, comment='章节标题'),
    sa.Column('chapter_outline', sa.Text(), nullable=False, comment='章节大纲'),
    sa.Column('previous_chapter_summary', sa.Text(), nullable=True, comment='前章摘要'),
    sa.Column('character_development', sa.Text(), nullable=True, comment='角色发展要求'),
    sa.Column('plot_requirements', sa.Text(), nullable=True, comment='情节要求'),
    sa.Column('target_word_count', sa.Integer(), nullable=False, comment='目标字数'),
    sa.Column('ai_provider', sa.Enum('ZHIPU', 'KIMI', name='aiprovider'), nullable=False, comment='AI提供商'),
    sa.Column('temperature', sa.Float(), nullable=False, comment='生成温度参数'),
    sa.Column('max_tokens', sa.Integer(), nullable=False, comment='最大生成token数'),
    sa.Column('status', sa.Enum('PENDING', 'GENERATING', 'COMPLETED', 'FAILED', name='generationstatus'), nullable=False, comment='生成状态'),
    sa.Column('generated_content', sa.Text(), nullable=True, comment='生成的章节内容'),
    sa.Column('error_message', sa.Text(), nullable=True, comment='错误信息'),
    sa.Column('content_length', sa.Integer(), nullable=True, comment='内容长度'),
    sa.Column('actual_word_count', sa.Integer(), nullable=True, comment='实际字数'),
    sa.Column('generation_time', sa.Float(), nullable=True, comment='生成耗时（秒）'),
    sa.Column('token_usage', sa.Integer(), nullable=True, comment='使用的token数量'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.Column('completed_at', sa.DateTime(), nullable=True, comment='完成时间'),
    sa.ForeignKeyConstraint(['story_bible_id'], ['story_bibles.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('chapters')
    op.drop_table('story_bibles')
    # ### end Alembic commands ###
