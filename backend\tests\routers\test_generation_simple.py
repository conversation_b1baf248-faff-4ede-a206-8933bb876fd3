"""
📝 [生成] 持久化路由简化测试
快速验证持久化路由的基本功能
"""

import pytest
from httpx import AsyncClient, ASGITransport
from fastapi import FastAPI

from app.core.database import init_database
from app.routers.generation import router
from app.core.config import log_debug, log_info


# 创建测试应用
def create_test_app() -> FastAPI:
    """创建测试用的FastAPI应用"""
    app = FastAPI()
    app.include_router(router)
    return app


@pytest.fixture
async def test_app():
    """测试应用fixture"""
    app = create_test_app()
    await init_database()
    return app


@pytest.fixture
async def async_client(test_app):
    """异步HTTP客户端fixture"""
    transport = ASGITransport(app=test_app)
    async with AsyncClient(transport=transport, base_url="http://test") as client:
        yield client


@pytest.fixture
def simple_bible_request():
    """简化的故事圣经请求数据"""
    return {
        "title": "测试小说：简单的魔法故事",
        "genre": "fantasy",
        "theme": "这是一个关于成长和友谊的简单魔法故事主题",
        "protagonist": "艾莉丝是一个年轻勇敢的魔法师学徒",
        "setting": "故事发生在一个现代化的魔法学院里面",
        "plot_outline": "主角艾莉丝在魔法学院学习魔法，遇到各种挑战，最终成长为优秀的魔法师",
        "target_audience": "面向青少年读者群体",
        "writing_style": "采用轻松幽默的写作风格",
        "ai_provider": "zhipu",
        "temperature": 0.7,
        "max_tokens": 2000
    }


class TestPersistenceSimple:
    """持久化路由简化测试类"""
    
    async def test_create_bible_basic(self, async_client: AsyncClient, simple_bible_request):
        """📝 [生成] 测试基本的故事圣经创建"""
        log_debug("测试", "开始测试基本故事圣经创建")
        
        response = await async_client.post(
            "/api/v1/generate-bible",
            json=simple_bible_request
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证基本响应结构
        assert "id" in data
        assert data["task_type"] == "story_bible"
        assert data["status"] == "pending"
        assert data["ai_provider"] == "zhipu"
        
        log_info("测试", "基本故事圣经创建测试通过", 任务ID=data["id"])
        return data["id"]
    
    async def test_list_bibles_empty(self, async_client: AsyncClient):
        """📁 [文件] 测试空的故事圣经列表"""
        log_debug("测试", "开始测试空故事圣经列表")
        
        response = await async_client.get("/api/v1/bibles")

        assert response.status_code == 200
        data = response.json()
        
        # 验证列表结构
        assert "total" in data
        assert "items" in data
        assert "limit" in data
        assert "offset" in data
        assert isinstance(data["items"], list)
        
        log_info("测试", "空故事圣经列表测试通过", 返回数量=data["total"])
    
    async def test_get_nonexistent_bible(self, async_client: AsyncClient):
        """❌ [错误] 测试获取不存在的故事圣经"""
        log_debug("测试", "开始测试获取不存在的故事圣经")
        
        response = await async_client.get("/api/v1/bible/nonexistent-id")
        
        assert response.status_code == 404
        data = response.json()
        assert "detail" in data
        assert "未找到故事圣经" in data["detail"]
        
        log_info("测试", "不存在故事圣经测试通过")
    
    async def test_create_and_get_bible(self, async_client: AsyncClient, simple_bible_request):
        """📝 [生成] 测试创建并获取故事圣经"""
        log_debug("测试", "开始测试创建并获取故事圣经")
        
        # 创建故事圣经
        create_response = await async_client.post(
            "/api/v1/generate-bible",
            json=simple_bible_request
        )
        assert create_response.status_code == 200
        bible_id = create_response.json()["id"]

        # 获取故事圣经
        get_response = await async_client.get(f"/api/v1/bible/{bible_id}")
        assert get_response.status_code == 200
        
        bible_data = get_response.json()
        assert bible_data["id"] == bible_id
        assert bible_data["title"] == simple_bible_request["title"]
        assert bible_data["genre"] == simple_bible_request["genre"]
        
        log_info("测试", "创建并获取故事圣经测试通过", 故事圣经ID=bible_id)
    
    async def test_delete_bible(self, async_client: AsyncClient, simple_bible_request):
        """🗑️ [删除] 测试删除故事圣经"""
        log_debug("测试", "开始测试删除故事圣经")
        
        # 创建故事圣经
        create_response = await async_client.post(
            "/api/v1/generate-bible",
            json=simple_bible_request
        )
        assert create_response.status_code == 200
        bible_id = create_response.json()["id"]

        # 删除故事圣经
        delete_response = await async_client.delete(f"/api/v1/bible/{bible_id}")
        assert delete_response.status_code == 200

        delete_data = delete_response.json()
        assert "message" in delete_data
        assert bible_id in delete_data["message"]

        # 验证已删除
        get_response = await async_client.get(f"/api/v1/bible/{bible_id}")
        assert get_response.status_code == 404
        
        log_info("测试", "删除故事圣经测试通过", 故事圣经ID=bible_id)
    
    async def test_chapter_without_bible(self, async_client: AsyncClient):
        """❌ [错误] 测试在没有故事圣经的情况下创建章节"""
        log_debug("测试", "开始测试无效故事圣经的章节创建")
        
        chapter_request = {
            "story_bible_id": "nonexistent-bible-id",
            "chapter_number": 1,
            "chapter_title": "测试章节标题",
            "chapter_outline": "这是一个测试章节的大纲内容",
            "target_word_count": 1000,
            "ai_provider": "zhipu",
            "temperature": 0.7,
            "max_tokens": 2000
        }
        
        response = await async_client.post(
            "/api/v1/generate-chapter",
            json=chapter_request
        )
        
        assert response.status_code == 404
        data = response.json()
        assert "detail" in data
        assert "未找到故事圣经" in data["detail"]
        
        log_info("测试", "无效故事圣经章节创建测试通过")
