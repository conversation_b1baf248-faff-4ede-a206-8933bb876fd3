"""
🧪 [测试] 情感物理映射器测试模块

测试情感识别、细节抽取和提示词增强功能

作者: 文心小说后端服务系统
创建时间: 2025-08-02
"""

import pytest
from unittest.mock import AsyncMock, MagicMock
from typing import List

from app.core.emotion_physical_mapper import (
    EmotionRecognizer, EmotionPhysicalMapper, EmotionMapping,
    EmotionEnhancedPrompt, create_emotion_mapper, quick_enhance_prompt
)
from app.models.emotional_gene import EmotionalGene
from app.repositories.emotional_gene_repo import EmotionalGeneRepository


class TestEmotionRecognizer:
    """情感识别器测试类"""
    
    def setup_method(self):
        """测试前准备"""
        self.recognizer = EmotionRecognizer()
    
    def test_recognize_basic_emotions(self):
        """测试基本情感识别"""
        # 测试单一情感
        emotions = self.recognizer.recognize_emotions("他非常愤怒地看着她")
        assert len(emotions) == 1
        assert emotions[0][0] == "愤怒"
        assert emotions[0][1] == 0.9  # "非常"修饰词
        
        # 测试多种情感
        emotions = self.recognizer.recognize_emotions("她既害怕又兴奋")
        assert len(emotions) == 2
        emotion_tags = [e[0] for e in emotions]
        assert "恐惧" in emotion_tags
        assert "喜悦" in emotion_tags
    
    def test_intensity_calculation(self):
        """测试强度计算"""
        # 测试不同强度修饰词
        test_cases = [
            ("极度愤怒", "愤怒", 1.0),
            ("很生气", "愤怒", 0.7),
            ("有些难过", "悲伤", 0.5),
            ("微微害怕", "恐惧", 0.1)  # 修改为"害怕"，这个在恐惧关键词列表中
        ]

        for text, expected_emotion, expected_intensity in test_cases:
            emotions = self.recognizer.recognize_emotions(text)
            assert len(emotions) >= 1, f"在文本'{text}'中未识别到任何情感"
            found = False
            for emotion, intensity in emotions:
                if emotion == expected_emotion:
                    assert intensity == expected_intensity, f"情感'{expected_emotion}'的强度不匹配，期望{expected_intensity}，实际{intensity}"
                    found = True
                    break
            assert found, f"未找到预期情感 {expected_emotion}，实际识别到的情感: {[e[0] for e in emotions]}"
    
    def test_emotion_deduplication(self):
        """测试情感去重"""
        # 同一情感的不同关键词应该合并
        emotions = self.recognizer.recognize_emotions("他愤怒地生气了")
        emotion_dict = {e[0]: e[1] for e in emotions}
        assert len(emotion_dict) == 1
        assert "愤怒" in emotion_dict
    
    def test_no_emotions_found(self):
        """测试未找到情感的情况"""
        emotions = self.recognizer.recognize_emotions("今天天气很好")
        assert len(emotions) == 0


class TestEmotionPhysicalMapper:
    """情感物理映射器测试类"""
    
    def setup_method(self):
        """测试前准备"""
        # 创建模拟的基因仓库
        self.mock_repo = AsyncMock(spec=EmotionalGeneRepository)
        self.mapper = EmotionPhysicalMapper(self.mock_repo)
        
        # 创建测试用的情感基因
        self.test_gene = EmotionalGene(
            id=1,
            emotion_tag="愤怒",
            source_text="他愤怒地握紧了拳头",
            physiological_reactions=["握紧拳头", "咬紧牙关", "血管突起"],
            sensory_triggers=["听到刺耳声音", "看到红色", "感受到热度"],
            entropy_items=["桌上的杯子微微震动", "远处传来汽车鸣笛声"],
            intensity_score=0.8,
            reliability_score=0.9,
            quality_score=0.85
        )
    
    @pytest.mark.asyncio
    async def test_extract_emotion_details_success(self):
        """测试成功抽取情感细节"""
        # 设置模拟返回
        self.mock_repo.search_genes.return_value = ([self.test_gene], 1)
        self.mock_repo.increment_usage.return_value = True
        
        # 执行测试
        mapping = await self.mapper.extract_emotion_details("愤怒", 0.8, 1)
        
        # 验证结果
        assert mapping.emotion_tag == "愤怒"
        assert mapping.intensity == 0.8
        assert len(mapping.physiological_reactions) > 0
        assert len(mapping.sensory_triggers) > 0
        assert len(mapping.entropy_items) > 0
        assert 1 in mapping.source_genes
        
        # 验证调用
        self.mock_repo.search_genes.assert_called_once()
        self.mock_repo.increment_usage.assert_called_once_with(1)
    
    @pytest.mark.asyncio
    async def test_extract_emotion_details_no_genes_found(self):
        """测试未找到基因时的处理"""
        # 设置模拟返回空结果
        self.mock_repo.search_genes.return_value = ([], 0)
        self.mock_repo.get_random_genes.return_value = [self.test_gene]
        self.mock_repo.increment_usage.return_value = True
        
        # 执行测试
        mapping = await self.mapper.extract_emotion_details("未知情感", 0.5, 1)
        
        # 验证结果
        assert mapping.emotion_tag == "未知情感"
        assert len(mapping.source_genes) > 0
        
        # 验证调用了随机获取
        self.mock_repo.get_random_genes.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_extract_emotion_details_exception_handling(self):
        """测试异常处理"""
        # 设置模拟抛出异常
        self.mock_repo.search_genes.side_effect = Exception("数据库错误")
        
        # 执行测试
        mapping = await self.mapper.extract_emotion_details("愤怒", 0.8, 1)
        
        # 验证返回空映射
        assert mapping.emotion_tag == "愤怒"
        assert mapping.intensity == 0.8
        assert len(mapping.physiological_reactions) == 0
        assert len(mapping.sensory_triggers) == 0
        assert len(mapping.entropy_items) == 0
        assert len(mapping.source_genes) == 0
    
    @pytest.mark.asyncio
    async def test_auto_recognize_and_extract(self):
        """测试自动识别和抽取"""
        # 设置模拟返回
        self.mock_repo.search_genes.return_value = ([self.test_gene], 1)
        self.mock_repo.increment_usage.return_value = True
        
        # 执行测试
        text = "他非常愤怒，同时感到害怕"
        mappings = await self.mapper.auto_recognize_and_extract(text, max_emotions=2)
        
        # 验证结果
        assert len(mappings) >= 1
        emotion_tags = [m.emotion_tag for m in mappings]
        assert "愤怒" in emotion_tags or "恐惧" in emotion_tags
    
    @pytest.mark.asyncio
    async def test_enhance_prompt_with_specified_emotions(self):
        """测试使用指定情感增强提示词"""
        # 设置模拟返回
        self.mock_repo.search_genes.return_value = ([self.test_gene], 1)
        self.mock_repo.increment_usage.return_value = True
        
        # 执行测试
        original_prompt = "请写一个关于冲突的场景"
        result = await self.mapper.enhance_prompt_with_emotions(
            original_prompt=original_prompt,
            specified_emotions=["愤怒"],
            emotion_intensity=0.8,
            auto_detect=False
        )
        
        # 验证结果
        assert isinstance(result, EmotionEnhancedPrompt)
        assert result.original_prompt == original_prompt
        assert len(result.enhanced_prompt) > len(original_prompt)
        assert len(result.emotion_mappings) == 1
        assert result.emotion_mappings[0].emotion_tag == "愤怒"
        assert len(result.enhancement_instructions) > 0
        
        # 验证增强后的提示词包含情感细节
        assert "情感细节强制要求" in result.enhanced_prompt
        assert "生理反应" in result.enhanced_prompt
    
    @pytest.mark.asyncio
    async def test_enhance_prompt_with_auto_detection(self):
        """测试自动检测情感增强提示词"""
        # 设置模拟返回
        self.mock_repo.search_genes.return_value = ([self.test_gene], 1)
        self.mock_repo.increment_usage.return_value = True
        
        # 执行测试
        original_prompt = "写一个愤怒的场景"
        result = await self.mapper.enhance_prompt_with_emotions(
            original_prompt=original_prompt,
            auto_detect=True
        )
        
        # 验证结果
        assert len(result.emotion_mappings) >= 1
        assert any(m.emotion_tag == "愤怒" for m in result.emotion_mappings)
    
    def test_generate_enhancement_instructions(self):
        """测试生成增强指令"""
        # 创建测试映射
        mapping = EmotionMapping(
            emotion_tag="愤怒",
            intensity=0.8,
            physiological_reactions=["握紧拳头", "咬紧牙关"],
            sensory_triggers=["听到刺耳声音"],
            entropy_items=["桌上的杯子震动"],
            source_genes=[1]
        )
        
        # 执行测试
        instructions = self.mapper._generate_enhancement_instructions([mapping])
        
        # 验证结果
        assert len(instructions) > 0
        instruction_text = "\n".join(instructions)
        assert "情感细节强制要求" in instruction_text
        assert "愤怒" in instruction_text
        assert "握紧拳头" in instruction_text
        assert "听到刺耳声音" in instruction_text
        assert "桌上的杯子震动" in instruction_text
    
    def test_build_enhanced_prompt(self):
        """测试构建增强提示词"""
        original_prompt = "写一个场景"
        instructions = ["【情感细节强制要求】", "1. 情感：愤怒"]
        
        enhanced_prompt = self.mapper._build_enhanced_prompt(original_prompt, instructions)
        
        assert original_prompt in enhanced_prompt
        assert "情感细节强制要求" in enhanced_prompt
        assert "严格按照以上情感细节要求" in enhanced_prompt


class TestConvenienceFunctions:
    """便捷函数测试类"""
    
    @pytest.mark.asyncio
    async def test_create_emotion_mapper(self):
        """测试创建情感映射器"""
        mock_repo = AsyncMock(spec=EmotionalGeneRepository)
        mapper = await create_emotion_mapper(mock_repo)
        
        assert isinstance(mapper, EmotionPhysicalMapper)
        assert mapper.gene_repository == mock_repo
    
    @pytest.mark.asyncio
    async def test_quick_enhance_prompt(self):
        """测试快速增强提示词"""
        # 创建模拟仓库
        mock_repo = AsyncMock(spec=EmotionalGeneRepository)
        test_gene = EmotionalGene(
            id=1,
            emotion_tag="愤怒",
            source_text="测试",
            physiological_reactions=["握紧拳头"],
            sensory_triggers=["听到声音"],
            entropy_items=["环境细节"],
            intensity_score=0.8,
            reliability_score=0.9,
            quality_score=0.85
        )
        mock_repo.search_genes.return_value = ([test_gene], 1)
        mock_repo.increment_usage.return_value = True
        
        # 执行测试
        original_prompt = "写一个场景"
        enhanced_prompt = await quick_enhance_prompt(
            prompt=original_prompt,
            gene_repository=mock_repo,
            emotions=["愤怒"],
            intensity=0.8
        )
        
        # 验证结果
        assert isinstance(enhanced_prompt, str)
        assert len(enhanced_prompt) > len(original_prompt)
        assert "情感细节强制要求" in enhanced_prompt
