"""
🧬 情感基因库API测试模块

测试情感基因库的各种API端点：
1. 搜索和检索接口
2. 管理操作接口
3. 统计分析接口
4. 批量操作接口

作者: 文心小说后端服务系统
创建时间: 2025-08-02
"""

import pytest
from httpx import AsyncClient, ASGITransport
from fastapi import FastAPI, status
from typing import Dict, Any

from app.core.database import init_database
from app.routers import emotion_genes
from app.core.config import log_debug, log_info


# 创建测试应用
def create_test_app() -> FastAPI:
    """创建测试用的FastAPI应用"""
    app = FastAPI()
    app.include_router(emotion_genes.router)
    return app


@pytest.fixture
async def test_app():
    """测试应用fixture"""
    app = create_test_app()
    await init_database()
    return app


@pytest.fixture
async def async_client(test_app):
    """异步HTTP客户端fixture"""
    transport = ASGITransport(app=test_app)
    async with AsyncClient(transport=transport, base_url="http://test") as client:
        yield client


class TestEmotionGenesAPI:
    """🧬 [情感基因API测试] 测试情感基因库API端点"""
    
    @pytest.fixture
    async def sample_gene_data(self) -> Dict[str, Any]:
        """创建测试用的基因数据"""
        return {
            "emotion_tag": "恐惧",
            "source_text": "他感到强烈的恐惧，心跳剧烈，呼吸急促。",
            "physiological_reactions": ["心跳加速", "呼吸急促", "手掌出汗"],
            "sensory_triggers": ["看到阴影", "听到脚步声"],
            "entropy_items": ["周围传来嘈杂的声音"],
            "intensity_score": 0.8,
            "reliability_score": 0.7,
            "purification_level": "basic",
            "category": "负面情感",
            "subcategory": "恐惧类",
            "source_type": "文学作品",
            "source_author": "测试作者",
            "source_title": "测试小说"
        }
    
    async def test_create_emotion_gene(self, async_client: AsyncClient, sample_gene_data: Dict[str, Any]):
        """测试创建情感基因API"""
        log_debug("测试", "开始测试创建情感基因API")
        
        response = await async_client.post("/emotion-genes/create", json=sample_gene_data)
        
        # 验证响应
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert data["emotion_tag"] == sample_gene_data["emotion_tag"]
        assert data["source_text"] == sample_gene_data["source_text"]
        assert data["physiological_reactions"] == sample_gene_data["physiological_reactions"]
        assert data["sensory_triggers"] == sample_gene_data["sensory_triggers"]
        assert data["entropy_items"] == sample_gene_data["entropy_items"]
        assert data["intensity_score"] == sample_gene_data["intensity_score"]
        assert data["reliability_score"] == sample_gene_data["reliability_score"]
        assert data["id"] is not None
        assert data["quality_score"] > 0.0
        assert data["created_at"] is not None
        
        log_info("测试", "创建情感基因API测试通过", 基因ID=data["id"])
        return data["id"]  # 返回ID供其他测试使用
    
    async def test_get_emotion_gene(self, async_client: AsyncClient, sample_gene_data: Dict[str, Any]):
        """测试获取单个情感基因API"""
        log_debug("测试", "开始测试获取情感基因API")
        
        # 先创建一个基因
        create_response = await async_client.post("/emotion-genes/create", json=sample_gene_data)
        gene_id = create_response.json()["id"]
        
        # 获取基因
        response = await async_client.get(f"/emotion-genes/{gene_id}")
        
        # 验证响应
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert data["id"] == gene_id
        assert data["emotion_tag"] == sample_gene_data["emotion_tag"]
        assert data["source_text"] == sample_gene_data["source_text"]
        assert data["usage_count"] >= 1  # 应该增加了使用次数
        
        log_info("测试", "获取情感基因API测试通过", 基因ID=gene_id)
    
    async def test_get_nonexistent_gene(self, async_client: AsyncClient):
        """测试获取不存在的情感基因"""
        log_debug("测试", "开始测试获取不存在的情感基因")
        
        response = await async_client.get("/emotion-genes/99999")
        
        # 验证响应
        assert response.status_code == status.HTTP_404_NOT_FOUND
        data = response.json()
        assert "不存在" in data["detail"]
        
        log_info("测试", "获取不存在情感基因API测试通过")
    
    async def test_search_emotion_genes(self, async_client: AsyncClient, sample_gene_data: Dict[str, Any]):
        """测试搜索情感基因API"""
        log_debug("测试", "开始测试搜索情感基因API")
        
        # 先创建几个基因
        gene1_data = sample_gene_data.copy()
        gene1_data["emotion_tag"] = "恐惧"
        gene1_data["category"] = "负面情感"
        
        gene2_data = sample_gene_data.copy()
        gene2_data["emotion_tag"] = "愤怒"
        gene2_data["category"] = "负面情感"
        
        gene3_data = sample_gene_data.copy()
        gene3_data["emotion_tag"] = "喜悦"
        gene3_data["category"] = "正面情感"
        
        await async_client.post("/emotion-genes/create", json=gene1_data)
        await async_client.post("/emotion-genes/create", json=gene2_data)
        await async_client.post("/emotion-genes/create", json=gene3_data)
        
        # 测试基本搜索
        response = await async_client.get("/emotion-genes/search")
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "genes" in data
        assert "total_count" in data
        assert data["total_count"] >= 3
        
        # 测试按情感标签搜索
        response = await async_client.get("/emotion-genes/search?emotion_tag=恐惧")
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["total_count"] >= 1
        assert all(gene["emotion_tag"] == "恐惧" for gene in data["genes"])
        
        # 测试按类别搜索
        response = await async_client.get("/emotion-genes/search?category=负面情感")
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["total_count"] >= 2
        assert all(gene["category"] == "负面情感" for gene in data["genes"])
        
        # 测试分页
        response = await async_client.get("/emotion-genes/search?limit=2&offset=0")
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data["genes"]) <= 2
        assert data["limit"] == 2
        assert data["offset"] == 0
        
        log_info("测试", "搜索情感基因API测试通过")
    
    async def test_get_random_genes(self, async_client: AsyncClient, sample_gene_data: Dict[str, Any]):
        """测试随机获取情感基因API"""
        log_debug("测试", "开始测试随机获取情感基因API")
        
        # 先创建几个基因
        for i in range(5):
            gene_data = sample_gene_data.copy()
            gene_data["emotion_tag"] = f"情感{i}"
            await async_client.post("/emotion-genes/create", json=gene_data)
        
        # 获取随机基因
        response = await async_client.get("/emotion-genes/random/genes?count=3")
        
        # 验证响应
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert isinstance(data, list)
        assert len(data) <= 3
        assert all("emotion_tag" in gene for gene in data)
        assert all("physiological_reactions" in gene for gene in data)
        
        log_info("测试", "随机获取情感基因API测试通过", 获取数量=len(data))
    
    async def test_get_high_quality_genes(self, async_client: AsyncClient, sample_gene_data: Dict[str, Any]):
        """测试获取高质量情感基因API"""
        log_debug("测试", "开始测试获取高质量情感基因API")
        
        # 创建高质量基因
        high_quality_data = sample_gene_data.copy()
        high_quality_data["intensity_score"] = 0.9
        high_quality_data["reliability_score"] = 0.8
        
        # 创建低质量基因
        low_quality_data = sample_gene_data.copy()
        low_quality_data["intensity_score"] = 0.3
        low_quality_data["reliability_score"] = 0.2
        
        await async_client.post("/emotion-genes/create", json=high_quality_data)
        await async_client.post("/emotion-genes/create", json=low_quality_data)
        
        # 获取高质量基因
        response = await async_client.get("/emotion-genes/high-quality/genes?threshold=0.7")
        
        # 验证响应
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert "genes" in data
        assert "total_count" in data
        assert "threshold" in data
        assert data["threshold"] == 0.7
        assert data["total_count"] >= 1
        assert all(gene["quality_score"] >= 0.7 for gene in data["genes"])
        
        log_info("测试", "获取高质量情感基因API测试通过", 高质量基因数=data["total_count"])
    
    async def test_get_statistics(self, async_client: AsyncClient, sample_gene_data: Dict[str, Any]):
        """测试获取统计信息API"""
        log_debug("测试", "开始测试获取统计信息API")
        
        # 先创建一些基因
        emotions = ["恐惧", "愤怒", "喜悦", "悲伤"]
        for emotion in emotions:
            gene_data = sample_gene_data.copy()
            gene_data["emotion_tag"] = emotion
            await async_client.post("/emotion-genes/create", json=gene_data)
        
        # 获取统计信息
        response = await async_client.get("/emotion-genes/statistics/overview")
        
        # 验证响应
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert "total_genes" in data
        assert "emotion_distribution" in data
        assert "quality_distribution" in data
        assert "average_quality" in data
        assert "average_intensity" in data
        assert "average_reliability" in data
        assert "high_quality_count" in data
        assert "recent_additions" in data
        assert "most_used_emotions" in data
        assert "source_type_distribution" in data
        
        assert data["total_genes"] >= 4
        assert len(data["emotion_distribution"]) >= 4
        assert data["average_quality"] > 0.0
        
        log_info("测试", "获取统计信息API测试通过", 总基因数=data["total_genes"])
    
    async def test_batch_seed_genes(self, async_client: AsyncClient):
        """测试批量填充情感基因API"""
        log_debug("测试", "开始测试批量填充情感基因API")
        
        # 准备批量数据
        dna_list = [
            {
                "emotion_tag": "恐惧",
                "source_text": "他感到恐惧",
                "physiological_reactions": ["心跳加速"],
                "sensory_triggers": ["看到阴影"],
                "entropy_items": ["环境噪音"],
                "intensity_score": 0.8,
                "reliability_score": 0.7,
                "purification_level": "basic"
            },
            {
                "emotion_tag": "愤怒",
                "source_text": "她感到愤怒",
                "physiological_reactions": ["脸色发红"],
                "sensory_triggers": ["听到侮辱"],
                "entropy_items": ["周围嘈杂"],
                "intensity_score": 0.9,
                "reliability_score": 0.8,
                "purification_level": "basic"
            }
        ]
        
        metadata = {
            "source_type": "测试数据",
            "source_author": "测试作者",
            "category": "测试类别"
        }
        
        # 执行批量填充
        response = await async_client.post(
            "/emotion-genes/batch/seed",
            json={"dna_list": dna_list, "metadata": metadata}
        )
        
        # 验证响应
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert "success_count" in data
        assert "total_count" in data
        assert "success_rate" in data
        assert "message" in data
        
        assert data["success_count"] == 2
        assert data["total_count"] == 2
        assert data["success_rate"] == 1.0
        
        log_info("测试", "批量填充情感基因API测试通过", 成功数量=data["success_count"])
