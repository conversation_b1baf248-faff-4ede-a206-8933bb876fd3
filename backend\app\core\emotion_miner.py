"""
🧬 [情感采矿] 情感采矿机模块

从文本源中批量提取包含特定情感关键词的段落，实现真实性过滤器。
这是情感基因库的第一步：从原始文本中挖掘包含真实情感反应的段落。

主要功能：
1. 文本段落提取 - 从长文本中分割出有意义的段落
2. 情感关键词匹配 - 识别包含特定情感的段落
3. 真实性过滤 - 优先保留包含生理细节的段落
4. 中文文本处理 - 支持jieba分词和中文情感分析

作者: AI小速写团队
创建时间: 2025-08-02
"""

import re
import jieba
import jieba.posseg as pseg
from typing import List, Dict, Set, Optional, Tuple, Any
from dataclasses import dataclass
from pathlib import Path
import logging
from app.core.config import settings, log_debug, log_info, log_error




@dataclass
class EmotionSegment:
    """
    🧬 [情感片段] 情感段落数据结构
    
    表示从文本中提取的包含特定情感的段落
    """
    text: str                    # 原始文本内容
    emotion_keywords: List[str]  # 匹配到的情感关键词
    confidence_score: float      # 置信度分数 (0-1)
    reality_score: float         # 真实性分数 (0-1)
    source_info: Dict[str, Any]  # 来源信息
    physiological_indicators: List[str]  # 生理反应指标
    sensory_details: List[str]   # 感官细节


class EmotionKeywordDict:
    """
    🎯 [情感词典] 情感关键词词典管理器
    
    管理各种情感类别的关键词，支持动态加载和扩展
    """
    
    def __init__(self):
        """初始化情感词典"""
        log_debug("情感词典", "初始化情感关键词词典")
        self._emotion_keywords = self._load_default_keywords()
        self._physiological_keywords = self._load_physiological_keywords()
        self._sensory_keywords = self._load_sensory_keywords()
        
    def _load_default_keywords(self) -> Dict[str, List[str]]:
        """🎯 [词典] 加载默认情感关键词"""
        return {
            "背叛": ["背叛", "出卖", "欺骗", "辜负", "不忠", "叛变", "背信弃义"],
            "狂喜": ["狂喜", "欣喜若狂", "兴奋", "激动", "狂欢", "欢呼", "雀跃"],
            "愤怒": ["愤怒", "暴怒", "怒火", "气愤", "恼怒", "发火", "暴躁"],
            "恐惧": ["恐惧", "害怕", "惊恐", "畏惧", "胆怯", "惊慌", "恐慌"],
            "悲伤": ["悲伤", "伤心", "难过", "痛苦", "哀伤", "忧伤", "沮丧"],
            "羞耻": ["羞耻", "羞愧", "惭愧", "丢脸", "尴尬", "难堪", "羞辱"],
            "嫉妒": ["嫉妒", "妒忌", "眼红", "羡慕", "不甘", "醋意", "妒火"],
            "绝望": ["绝望", "无望", "失望", "沮丧", "颓废", "绝境", "崩溃"],
            "爱恋": ["爱恋", "爱情", "深爱", "迷恋", "痴情", "相思", "眷恋"],
            "孤独": ["孤独", "寂寞", "孤单", "独自", "落寞", "孤寂", "形单影只"]
        }
    
    def _load_physiological_keywords(self) -> List[str]:
        """🫀 [生理] 加载生理反应关键词"""
        return [
            # 心脏相关
            "心跳", "心率", "心脏", "胸口", "心悸", "心慌",
            # 呼吸相关  
            "呼吸", "喘息", "气息", "窒息", "屏息", "喘气",
            # 血液循环
            "脸红", "苍白", "血液", "脉搏", "血管", "发热",
            # 肌肉反应
            "颤抖", "发抖", "僵硬", "紧绷", "痉挛", "抽搐",
            # 消化系统
            "胃部", "恶心", "反胃", "肠胃", "食欲", "干呕",
            # 神经系统
            "头晕", "眩晕", "头痛", "神经", "麻木", "刺痛",
            # 分泌系统
            "出汗", "冷汗", "汗水", "眼泪", "流泪", "口干"
        ]
    
    def _load_sensory_keywords(self) -> List[str]:
        """👁️ [感官] 加载感官细节关键词"""
        return [
            # 视觉
            "看见", "看到", "眼前", "视线", "目光", "眼神", "瞳孔",
            # 听觉
            "听见", "听到", "声音", "响声", "噪音", "寂静", "耳朵",
            # 触觉
            "触摸", "感觉", "温度", "冰冷", "温暖", "粗糙", "光滑",
            # 嗅觉
            "闻到", "气味", "香味", "臭味", "芳香", "刺鼻", "鼻子",
            # 味觉
            "尝到", "味道", "甜味", "苦味", "酸味", "辣味", "舌头"
        ]
    
    def get_emotion_keywords(self, emotion: str) -> List[str]:
        """获取指定情感的关键词"""
        return self._emotion_keywords.get(emotion, [])
    
    def get_all_emotions(self) -> List[str]:
        """获取所有情感类别"""
        return list(self._emotion_keywords.keys())
    
    def get_physiological_keywords(self) -> List[str]:
        """获取生理反应关键词"""
        return self._physiological_keywords
    
    def get_sensory_keywords(self) -> List[str]:
        """获取感官细节关键词"""
        return self._sensory_keywords


class TextSegmenter:
    """
    📝 [文本分割] 文本段落分割器
    
    将长文本分割成有意义的段落，为后续情感分析做准备
    """
    
    def __init__(self):
        """初始化文本分割器"""
        log_debug("文本分割", "初始化文本段落分割器")
        
    def segment_by_paragraphs(self, text: str) -> List[str]:
        """📝 [分割] 按段落分割文本"""
        log_debug("文本分割", "开始按段落分割文本", 文本长度=len(text))
        
        # 按换行符分割
        paragraphs = text.split('\n')
        
        # 过滤空段落和过短段落
        valid_paragraphs = []
        for para in paragraphs:
            para = para.strip()
            if len(para) >= 20:  # 至少20个字符
                valid_paragraphs.append(para)
        
        log_info("文本分割", "段落分割完成", 原始段落数=len(paragraphs), 有效段落数=len(valid_paragraphs))
        return valid_paragraphs
    
    def segment_by_sentences(self, text: str, min_length: int = 50) -> List[str]:
        """📝 [分割] 按句子分割文本"""
        log_debug("文本分割", "开始按句子分割文本", 文本长度=len(text), 最小长度=min_length)
        
        # 中文句子分割正则表达式
        sentence_pattern = r'[。！？；\n]+'
        sentences = re.split(sentence_pattern, text)
        
        # 过滤空句子和过短句子
        valid_sentences = []
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) >= min_length:
                valid_sentences.append(sentence)
        
        log_info("文本分割", "句子分割完成", 原始句子数=len(sentences), 有效句子数=len(valid_sentences))
        return valid_sentences


class RealityFilter:
    """
    🎯 [真实性过滤] 真实性过滤器
    
    评估文本段落的真实性，优先保留包含生理细节、摒弃过多修辞手法的段落
    """
    
    def __init__(self, keyword_dict: EmotionKeywordDict):
        """初始化真实性过滤器"""
        self.keyword_dict = keyword_dict
        log_debug("真实性过滤", "初始化真实性过滤器")
        
        # 修辞手法关键词（降低真实性分数）
        self.rhetoric_keywords = [
            "如同", "仿佛", "好像", "犹如", "宛如", "似乎", "恰似",
            "比作", "譬如", "象征", "隐喻", "暗示", "寓意"
        ]
        
    def calculate_reality_score(self, text: str) -> float:
        """🎯 [评分] 计算文本的真实性分数"""
        log_debug("真实性评分", "开始计算真实性分数", 文本长度=len(text))
        
        score = 0.5  # 基础分数
        
        # 检查生理反应指标（提高分数）
        physiological_count = 0
        for keyword in self.keyword_dict.get_physiological_keywords():
            if keyword in text:
                physiological_count += 1
                score += 0.1
        
        # 检查感官细节（提高分数）
        sensory_count = 0
        for keyword in self.keyword_dict.get_sensory_keywords():
            if keyword in text:
                sensory_count += 1
                score += 0.05
        
        # 检查修辞手法（降低分数）
        rhetoric_count = 0
        for keyword in self.rhetoric_keywords:
            if keyword in text:
                rhetoric_count += 1
                score -= 0.1
        
        # 确保分数在0-1范围内
        score = max(0.0, min(1.0, score))
        
        log_debug("真实性评分", "真实性分数计算完成", 
                 分数=score, 生理指标=physiological_count, 
                 感官细节=sensory_count, 修辞手法=rhetoric_count)
        
        return score
    
    def extract_physiological_indicators(self, text: str) -> List[str]:
        """🫀 [提取] 提取生理反应指标"""
        indicators = []
        for keyword in self.keyword_dict.get_physiological_keywords():
            if keyword in text:
                indicators.append(keyword)
        return indicators
    
    def extract_sensory_details(self, text: str) -> List[str]:
        """👁️ [提取] 提取感官细节"""
        details = []
        for keyword in self.keyword_dict.get_sensory_keywords():
            if keyword in text:
                details.append(keyword)
        return details


class EmotionMiner:
    """
    🧬 [情感采矿机] 主要的情感采矿引擎
    
    从文本源中批量提取包含特定情感关键词的段落，实现智能过滤和评分
    """
    
    def __init__(self):
        """初始化情感采矿机"""
        log_info("情感采矿", "初始化情感采矿机引擎")
        
        self.keyword_dict = EmotionKeywordDict()
        self.text_segmenter = TextSegmenter()
        self.reality_filter = RealityFilter(self.keyword_dict)
        
        # 初始化jieba分词
        jieba.initialize()
        log_info("情感采矿", "情感采矿机初始化完成")

    def mine_emotions_from_text(self, text: str, target_emotions: Optional[List[str]] = None,
                               min_reality_score: float = 0.3) -> List[EmotionSegment]:
        """
        🧬 [采矿] 从文本中挖掘情感段落

        Args:
            text: 待分析的文本
            target_emotions: 目标情感列表，None表示所有情感
            min_reality_score: 最小真实性分数阈值

        Returns:
            List[EmotionSegment]: 提取的情感段落列表
        """
        log_info("情感采矿", "开始从文本中挖掘情感段落",
                文本长度=len(text), 目标情感=target_emotions, 最小真实性=min_reality_score)

        # 如果没有指定目标情感，使用所有情感
        if target_emotions is None:
            target_emotions = self.keyword_dict.get_all_emotions()

        # 分割文本为段落
        paragraphs = self.text_segmenter.segment_by_paragraphs(text)

        emotion_segments = []

        for i, paragraph in enumerate(paragraphs):
            log_debug("情感采矿", f"分析第{i+1}个段落", 段落长度=len(paragraph))

            # 检查每个目标情感
            for emotion in target_emotions:
                segment = self._analyze_paragraph_for_emotion(paragraph, emotion, i)

                if segment and segment.reality_score >= min_reality_score:
                    emotion_segments.append(segment)
                    log_debug("情感采矿", f"发现{emotion}情感段落",
                             真实性分数=segment.reality_score, 置信度=segment.confidence_score)

        log_info("情感采矿", "情感挖掘完成",
                总段落数=len(paragraphs), 提取段落数=len(emotion_segments))

        return emotion_segments

    def mine_emotions_from_file(self, file_path: str, target_emotions: Optional[List[str]] = None,
                               min_reality_score: float = 0.3) -> List[EmotionSegment]:
        """
        📁 [文件采矿] 从文件中挖掘情感段落

        Args:
            file_path: 文件路径
            target_emotions: 目标情感列表
            min_reality_score: 最小真实性分数阈值

        Returns:
            List[EmotionSegment]: 提取的情感段落列表
        """
        log_info("文件采矿", "开始从文件挖掘情感", 文件路径=file_path)

        try:
            # 读取文件内容
            file_path_obj = Path(file_path)
            if not file_path_obj.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")

            # 尝试不同编码读取文件
            encodings = ['utf-8', 'gbk', 'gb2312']
            text = None

            for encoding in encodings:
                try:
                    with open(file_path_obj, 'r', encoding=encoding) as f:
                        text = f.read()
                    log_debug("文件采矿", f"成功使用{encoding}编码读取文件")
                    break
                except UnicodeDecodeError:
                    continue

            if text is None:
                raise ValueError(f"无法读取文件，尝试了编码: {encodings}")

            # 添加文件来源信息
            segments = self.mine_emotions_from_text(text, target_emotions, min_reality_score)

            # 为每个段落添加文件来源信息
            for segment in segments:
                segment.source_info.update({
                    "source_type": "file",
                    "file_path": str(file_path_obj),
                    "file_name": file_path_obj.name
                })

            log_info("文件采矿", "文件情感挖掘完成",
                    文件大小=len(text), 提取段落数=len(segments))

            return segments

        except Exception as e:
            log_error("文件采矿", "文件情感挖掘失败", error=e, 文件路径=file_path)
            raise

    def _analyze_paragraph_for_emotion(self, paragraph: str, emotion: str, paragraph_index: int) -> Optional[EmotionSegment]:
        """
        🔍 [分析] 分析段落是否包含指定情感

        Args:
            paragraph: 待分析段落
            emotion: 目标情感
            paragraph_index: 段落索引

        Returns:
            Optional[EmotionSegment]: 情感段落对象，如果不匹配则返回None
        """
        # 获取情感关键词
        emotion_keywords = self.keyword_dict.get_emotion_keywords(emotion)

        # 检查是否包含情感关键词
        matched_keywords = []
        for keyword in emotion_keywords:
            if keyword in paragraph:
                matched_keywords.append(keyword)

        # 如果没有匹配的关键词，返回None
        if not matched_keywords:
            return None

        # 计算置信度分数（基于匹配关键词数量和段落长度）
        confidence_score = min(1.0, len(matched_keywords) * 0.3 + len(paragraph) / 1000)

        # 计算真实性分数
        reality_score = self.reality_filter.calculate_reality_score(paragraph)

        # 提取生理指标和感官细节
        physiological_indicators = self.reality_filter.extract_physiological_indicators(paragraph)
        sensory_details = self.reality_filter.extract_sensory_details(paragraph)

        # 创建情感段落对象
        segment = EmotionSegment(
            text=paragraph,
            emotion_keywords=matched_keywords,
            confidence_score=confidence_score,
            reality_score=reality_score,
            source_info={
                "emotion_type": emotion,
                "paragraph_index": paragraph_index,
                "extraction_time": "2025-08-02"  # 可以使用datetime.now()
            },
            physiological_indicators=physiological_indicators,
            sensory_details=sensory_details
        )

        return segment

    def batch_mine_from_directory(self, directory_path: str, file_pattern: str = "*.txt",
                                 target_emotions: Optional[List[str]] = None,
                                 min_reality_score: float = 0.3) -> List[EmotionSegment]:
        """
        📁 [批量采矿] 从目录中批量挖掘情感段落

        Args:
            directory_path: 目录路径
            file_pattern: 文件匹配模式
            target_emotions: 目标情感列表
            min_reality_score: 最小真实性分数阈值

        Returns:
            List[EmotionSegment]: 所有文件提取的情感段落列表
        """
        log_info("批量采矿", "开始批量挖掘目录中的情感",
                目录=directory_path, 文件模式=file_pattern)

        try:
            directory = Path(directory_path)
            if not directory.exists():
                raise FileNotFoundError(f"目录不存在: {directory_path}")

            # 查找匹配的文件
            files = list(directory.glob(file_pattern))
            log_info("批量采矿", f"找到{len(files)}个匹配文件")

            all_segments = []

            for file_path in files:
                log_debug("批量采矿", f"处理文件: {file_path.name}")

                try:
                    segments = self.mine_emotions_from_file(str(file_path), target_emotions, min_reality_score)
                    all_segments.extend(segments)

                except Exception as e:
                    log_error("批量采矿", f"处理文件失败: {file_path.name}", error=e)
                    continue

            log_info("批量采矿", "批量挖掘完成",
                    处理文件数=len(files), 总提取段落数=len(all_segments))

            return all_segments

        except Exception as e:
            log_error("批量采矿", "批量挖掘失败", error=e, 目录=directory_path)
            raise

    def get_mining_statistics(self, segments: List[EmotionSegment]) -> Dict[str, Any]:
        """
        📊 [统计] 获取挖掘统计信息

        Args:
            segments: 情感段落列表

        Returns:
            Dict[str, Any]: 统计信息
        """
        if not segments:
            return {"total_segments": 0}

        # 按情感类型统计
        emotion_stats = {}
        reality_scores = []
        confidence_scores = []

        for segment in segments:
            emotion_type = segment.source_info.get("emotion_type", "unknown")
            if emotion_type not in emotion_stats:
                emotion_stats[emotion_type] = 0
            emotion_stats[emotion_type] += 1

            reality_scores.append(segment.reality_score)
            confidence_scores.append(segment.confidence_score)

        # 计算平均分数
        avg_reality = sum(reality_scores) / len(reality_scores)
        avg_confidence = sum(confidence_scores) / len(confidence_scores)

        statistics = {
            "total_segments": len(segments),
            "emotion_distribution": emotion_stats,
            "average_reality_score": round(avg_reality, 3),
            "average_confidence_score": round(avg_confidence, 3),
            "high_quality_segments": len([s for s in segments if s.reality_score >= 0.7]),
            "physiological_segments": len([s for s in segments if s.physiological_indicators]),
            "sensory_segments": len([s for s in segments if s.sensory_details])
        }

        log_info("挖掘统计", "统计信息生成完成", **statistics)
        return statistics
