Metadata-Version: 2.4
Name: wenxin-novel-backend
Version: 0.1.0
Summary: 文心小说后端服务系统 - FastAPI后端服务，支持智谱AI和Kimi AI的多AI服务架构
Project-URL: Homepage, https://github.com/ai-novel/wenxin-novel-backend
Project-URL: Documentation, https://github.com/ai-novel/wenxin-novel-backend/docs
Project-URL: Repository, https://github.com/ai-novel/wenxin-novel-backend
Project-URL: Bug Tracker, https://github.com/ai-novel/wenxin-novel-backend/issues
Author-email: AI小速写团队 <<EMAIL>>
License: MIT
Requires-Python: >=3.11
Requires-Dist: aiosqlite>=0.19.0
Requires-Dist: alembic>=1.13.0
Requires-Dist: fastapi>=0.110.0
Requires-Dist: httpx>=0.26.0
Requires-Dist: jieba>=0.42.1
Requires-Dist: passlib[bcrypt]>=1.7.4
Requires-Dist: pydantic-settings>=2.1.0
Requires-Dist: pydantic>=2.5.0
Requires-Dist: python-dotenv>=1.0.0
Requires-Dist: python-jose[cryptography]>=3.3.0
Requires-Dist: python-multipart>=0.0.6
Requires-Dist: sqlalchemy[asyncio]>=2.0.25
Requires-Dist: structlog>=23.2.0
Requires-Dist: tenacity>=8.2.0
Requires-Dist: uvicorn[standard]>=0.25.0
Provides-Extra: dev
Requires-Dist: black>=23.12.0; extra == 'dev'
Requires-Dist: flake8>=6.1.0; extra == 'dev'
Requires-Dist: httpx>=0.26.0; extra == 'dev'
Requires-Dist: isort>=5.13.0; extra == 'dev'
Requires-Dist: mypy>=1.8.0; extra == 'dev'
Requires-Dist: pre-commit>=3.6.0; extra == 'dev'
Requires-Dist: pytest-asyncio>=0.21.0; extra == 'dev'
Requires-Dist: pytest-cov>=4.1.0; extra == 'dev'
Requires-Dist: pytest-httpx>=0.26.0; extra == 'dev'
Requires-Dist: pytest>=7.4.0; extra == 'dev'
Provides-Extra: test
Requires-Dist: httpx>=0.26.0; extra == 'test'
Requires-Dist: pytest-asyncio>=0.21.0; extra == 'test'
Requires-Dist: pytest-cov>=4.1.0; extra == 'test'
Requires-Dist: pytest-httpx>=0.26.0; extra == 'test'
Requires-Dist: pytest>=7.4.0; extra == 'test'
Description-Content-Type: text/markdown

# 文心小说后端服务系统

基于FastAPI的AI小说生成后端服务，支持智谱AI和Kimi AI多AI服务架构。

## 功能特性

- ✨ **智能故事生成**: 基于智谱AI GLM-4.5模型生成高质量故事圣经
- 📖 **章节生成**: 根据故事圣经智能生成具体章节内容
- 🔄 **异步处理**: 支持后台异步生成，提供任务状态查询
- 🌐 **RESTful API**: 提供完整的REST API接口
- 🔐 **多AI支持**: 支持智谱AI和Kimi AI多种服务提供商
- 📝 **结构化日志**: 采用中文结构化日志系统
- ✅ **全面测试**: 包含完整的单元测试和集成测试

## 技术栈

- **框架**: FastAPI 0.110+
- **Python**: 3.11+
- **AI服务**: 智谱AI GLM-4.5、Kimi AI
- **数据库**: SQLite + SQLAlchemy
- **日志**: Structlog
- **测试**: pytest + httpx
- **包管理**: uv

## 快速开始

### 1. 环境准备

```bash
# 安装uv包管理器
pip install uv

# 克隆项目
git clone <repository-url>
cd backend
```

### 2. 依赖安装

```bash
# 创建虚拟环境
uv venv

# 激活虚拟环境
source .venv/bin/activate  # Linux/Mac
# 或
.venv\Scripts\activate     # Windows

# 安装依赖
uv pip sync requirements.txt
```

### 3. 环境配置

复制并配置环境变量文件：

```bash
cp .env.example .env
```

编辑 `.env` 文件，配置API密钥：

```env
# 智谱AI配置
ZHIPU_API_KEY=your_zhipu_api_key
ZHIPU_MODEL=glm-4.5

# Kimi AI配置
KIMI_API_KEY=your_kimi_api_key
KIMI_MODEL=moonshot-v1-8k

# 默认AI提供商
DEFAULT_AI_PROVIDER=zhipu
```

### 4. 启动服务

```bash
# 开发模式启动
python main.py

# 或使用uvicorn
uvicorn main:app --reload --host 127.0.0.1 --port 8000
```

服务启动后访问：
- API文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/health

## API接口

### 核心接口

- `POST /api/v1/generate-bible` - 生成故事圣经
- `POST /api/v1/generate-chapter` - 生成章节内容
- `GET /api/v1/task/{task_id}` - 查询任务状态
- `GET /api/v1/tasks` - 获取任务列表

### 系统接口

- `GET /health` - 健康检查
- `GET /` - 根路径信息
- `GET /docs` - API文档

## 开发指南

### 运行测试

```bash
# 安装测试依赖
uv pip install -e .[test]

# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_main.py

# 生成覆盖率报告
pytest --cov=app --cov-report=html
```

### 代码质量检查

```bash
# 代码格式化
black app tests

# 导入排序
isort app tests

# 类型检查
mypy app

# 代码检查
flake8 app tests
```

## 项目结构

```
backend/
├── app/                    # 应用核心代码
│   ├── core/              # 核心配置
│   ├── routers/           # API路由
│   ├── services/          # 业务服务
│   ├── schemas/           # 数据模型
│   ├── models/            # 数据库模型
│   └── utils/             # 工具函数
├── tests/                 # 测试用例
├── data/                  # 数据文件
├── logs/                  # 日志文件
├── main.py               # 应用入口
├── pyproject.toml        # 项目配置
├── requirements.txt      # 依赖锁定文件
└── .env                  # 环境变量
```

## 日志系统

系统采用结构化中文日志，支持以下分类：

- 🔐 [认证] - 用户认证相关
- 📝 [生成] - AI内容生成相关
- 🌐 [API] - API调用相关
- 🏗️ [系统] - 系统初始化相关
- 🔧 [调试] - 调试信息
- ❌ [错误] - 错误信息
- ✅ [成功] - 成功操作

## 部署说明

### Docker部署

```bash
# 构建镜像
docker build -t wenxin-novel-backend .

# 运行容器
docker run -p 8000:8000 --env-file .env wenxin-novel-backend
```

### 生产环境部署

```bash
# 使用Gunicorn
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目地址: https://github.com/ai-novel/wenxin-novel-backend
- 问题反馈: https://github.com/ai-novel/wenxin-novel-backend/issues