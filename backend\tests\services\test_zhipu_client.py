"""
🌐 [API] 智谱AI客户端测试用例
使用pytest-httpx模拟HTTP请求，测试智谱AI客户端的各种场景
"""

import pytest
import httpx
from unittest.mock import AsyncMock, patch
from app.services.zhipu_client import (
    ZhipuClient, ChatMessage, ZhipuAIError,
    get_zhipu_client, close_zhipu_client
)


@pytest.fixture
async def zhipu_client():
    """创建智谱AI客户端测试实例"""
    client = ZhipuClient()
    yield client
    await client.close()


@pytest.fixture
def mock_chat_response():
    """模拟智谱AI聊天响应"""
    return {
        "id": "chatcmpl-test123",
        "object": "chat.completion",
        "created": 1677652288,
        "model": "glm-4.5",
        "choices": [
            {
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": "这是一个测试响应内容。"
                },
                "finish_reason": "stop"
            }
        ],
        "usage": {
            "prompt_tokens": 10,
            "completion_tokens": 15,
            "total_tokens": 25
        }
    }


@pytest.fixture
def sample_messages():
    """示例聊天消息"""
    return [
        ChatMessage(role="system", content="你是一个专业的小说创作助手。"),
        ChatMessage(role="user", content="请帮我写一个故事大纲。")
    ]


class TestZhipuClient:
    """智谱AI客户端测试类"""
    
    @pytest.mark.asyncio
    async def test_client_initialization(self, zhipu_client):
        """
        🏗️ [系统] 测试客户端初始化
        验证客户端正确初始化各项配置
        """
        assert zhipu_client.api_key is not None
        assert zhipu_client.base_url.endswith("/api/paas/v4")
        assert zhipu_client.model == "glm-4.5"
        assert zhipu_client.client is not None
    
    @pytest.mark.asyncio
    async def test_get_headers(self, zhipu_client):
        """
        🔐 [认证] 测试请求头生成
        验证请求头包含正确的认证信息
        """
        headers = zhipu_client._get_headers()
        
        assert "Authorization" in headers
        assert headers["Authorization"].startswith("Bearer ")
        assert headers["Content-Type"] == "application/json"
        assert headers["User-Agent"] == "wenxin-novel-backend/0.1.0"
    
    @pytest.mark.asyncio
    async def test_chat_completion_success(self, zhipu_client, sample_messages, mock_chat_response):
        """
        ✅ [成功] 测试聊天补全成功场景
        模拟智谱AI API返回成功响应
        """
        # 模拟HTTP响应
        with patch.object(zhipu_client.client, 'post') as mock_post:
            mock_response = AsyncMock()
            mock_response.status_code = 200
            mock_response.json = AsyncMock(return_value=mock_chat_response)
            mock_post.return_value = mock_response
            
            # 执行聊天补全
            response = await zhipu_client.chat_completion(
                messages=sample_messages,
                temperature=0.7,
                max_tokens=1000
            )
            
            # 验证响应
            assert response.id == "chatcmpl-test123"
            assert response.model == "glm-4.5"
            assert len(response.choices) == 1
            assert response.choices[0]["message"]["content"] == "这是一个测试响应内容。"
            assert response.usage["total_tokens"] == 25
            
            # 验证HTTP请求参数
            mock_post.assert_called_once()
            call_args = mock_post.call_args
            assert call_args[1]["url"].endswith("/chat/completions")
            
            request_data = call_args[1]["json"]
            assert request_data["model"] == "glm-4.5"
            assert request_data["temperature"] == 0.7
            assert request_data["max_tokens"] == 1000
            assert len(request_data["messages"]) == 2
    
    @pytest.mark.asyncio
    async def test_chat_completion_http_error(self, zhipu_client, sample_messages):
        """
        ❌ [错误] 测试HTTP错误处理
        验证客户端正确处理HTTP错误状态码
        """
        # 模拟HTTP 400错误响应
        with patch.object(zhipu_client.client, 'post') as mock_post:
            mock_response = AsyncMock()
            mock_response.status_code = 400
            mock_response.json = AsyncMock(return_value={
                "error": {
                    "message": "请求参数无效",
                    "type": "invalid_request"
                }
            })
            mock_response.text = "Bad Request"
            mock_post.return_value = mock_response
            
            # 验证抛出正确的异常
            with pytest.raises(ZhipuAIError) as exc_info:
                await zhipu_client.chat_completion(messages=sample_messages)
            
            error = exc_info.value
            assert error.status_code == 400
            assert "请求参数无效" in error.message or "HTTP 400" in error.message
            assert error.error_type == "invalid_request" or error.error_type == "http_error"
    
    @pytest.mark.asyncio
    async def test_chat_completion_timeout(self, zhipu_client, sample_messages):
        """
        ⏱️ [性能] 测试超时处理
        验证客户端正确处理网络超时
        """
        with patch.object(zhipu_client.client, 'post') as mock_post:
            mock_post.side_effect = httpx.TimeoutException("Request timeout")
            
            with pytest.raises(ZhipuAIError) as exc_info:
                await zhipu_client.chat_completion(messages=sample_messages)
            
            error = exc_info.value
            assert error.error_type == "timeout"
            assert "超时" in error.message
    
    @pytest.mark.asyncio
    async def test_chat_completion_connection_error(self, zhipu_client, sample_messages):
        """
        🌐 [API] 测试连接错误处理
        验证客户端正确处理网络连接错误
        """
        with patch.object(zhipu_client.client, 'post') as mock_post:
            mock_post.side_effect = httpx.ConnectError("Connection failed")
            
            with pytest.raises(ZhipuAIError) as exc_info:
                await zhipu_client.chat_completion(messages=sample_messages)
            
            error = exc_info.value
            assert error.error_type == "connection"
            assert "连接" in error.message
    
    @pytest.mark.asyncio
    async def test_chat_completion_stream_success(self, zhipu_client, sample_messages):
        """
        🔄 [任务] 测试流式聊天补全成功场景
        验证流式输出正确处理SSE格式数据
        """
        # 模拟流式响应数据
        stream_data = [
            b'data: {"id":"chatcmpl-test","choices":[{"index":0,"delta":{"role":"assistant"},"finish_reason":null}]}\n\n',
            b'data: {"id":"chatcmpl-test","choices":[{"index":0,"delta":{"content":"\\u4f60\\u597d"},"finish_reason":null}]}\n\n',
            b'data: {"id":"chatcmpl-test","choices":[{"index":0,"delta":{"content":"\\uff01"},"finish_reason":null}]}\n\n',
            b'data: {"id":"chatcmpl-test","choices":[{"index":0,"delta":{},"finish_reason":"stop"}]}\n\n',
            b'data: [DONE]\n\n'
        ]
        
        async def mock_aiter_bytes(chunk_size):
            for chunk in stream_data:
                yield chunk
        
        # 模拟流式响应
        with patch.object(zhipu_client.client, 'stream') as mock_stream:
            mock_response = AsyncMock()
            mock_response.status_code = 200
            mock_response.aiter_bytes = mock_aiter_bytes
            
            mock_stream.return_value.__aenter__.return_value = mock_response
            
            # 收集流式响应内容
            content_chunks = []
            async for chunk in zhipu_client.chat_completion_stream(messages=sample_messages):
                content_chunks.append(chunk)
            
            # 验证流式内容
            assert len(content_chunks) == 2
            assert content_chunks[0] == "你好"
            assert content_chunks[1] == "！"
    
    @pytest.mark.asyncio
    async def test_client_context_manager(self):
        """
        🏗️ [系统] 测试客户端上下文管理器
        验证异步上下文管理器正确工作
        """
        async with ZhipuClient() as client:
            assert client.client is not None
            
        # 验证客户端在退出上下文后被关闭
        # 注意：这里无法直接验证连接是否关闭，因为httpx.AsyncClient没有公开的状态


class TestZhipuClientSingleton:
    """智谱AI客户端单例测试类"""
    
    @pytest.mark.asyncio
    async def test_get_zhipu_client_singleton(self):
        """
        🏗️ [系统] 测试客户端单例模式
        验证get_zhipu_client返回同一个实例
        """
        # 清理可能存在的客户端实例
        await close_zhipu_client()
        
        # 获取两次客户端实例
        client1 = await get_zhipu_client()
        client2 = await get_zhipu_client()
        
        # 验证是同一个实例
        assert client1 is client2
        
        # 清理
        await close_zhipu_client()
    
    @pytest.mark.asyncio
    async def test_close_zhipu_client(self):
        """
        🔧 [调试] 测试客户端关闭功能
        验证close_zhipu_client正确关闭客户端
        """
        # 获取客户端实例
        client = await get_zhipu_client()
        assert client is not None
        
        # 关闭客户端
        await close_zhipu_client()
        
        # 再次获取应该是新的实例
        new_client = await get_zhipu_client()
        assert new_client is not client
        
        # 清理
        await close_zhipu_client()