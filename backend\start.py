#!/usr/bin/env python
"""
🚀 [启动] 文心小说后端服务启动脚本
用于启动开发和生产环境的FastAPI服务
"""

import sys
import os
import argparse
import uvicorn
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.config import settings, log_info


def main():
    """主启动函数"""
    parser = argparse.ArgumentParser(description='启动文心小说后端服务')
    parser.add_argument(
        '--host', 
        default=settings.api_host, 
        help=f'服务主机地址 (默认: {settings.api_host})'
    )
    parser.add_argument(
        '--port', 
        type=int, 
        default=settings.api_port, 
        help=f'服务端口 (默认: {settings.api_port})'
    )
    parser.add_argument(
        '--reload', 
        action='store_true', 
        default=settings.api_reload,
        help='启用热重载 (开发模式)'
    )
    parser.add_argument(
        '--workers', 
        type=int, 
        default=1,
        help='工作进程数 (生产模式)'
    )
    parser.add_argument(
        '--log-level', 
        default=settings.log_level.lower(),
        choices=['debug', 'info', 'warning', 'error'],
        help=f'日志级别 (默认: {settings.log_level.lower()})'
    )
    parser.add_argument(
        '--env', 
        choices=['dev', 'prod'],
        default='dev',
        help='运行环境 (默认: dev)'
    )
    
    args = parser.parse_args()
    
    # 检查环境变量文件
    env_file = project_root / '.env'
    if not env_file.exists():
        print("❌ 错误: 未找到 .env 文件")
        print("请确保项目根目录存在 .env 文件并配置了必要的环境变量")
        print("参考 .env.example 创建配置文件")
        sys.exit(1)
    
    # 检查API密钥
    if not settings.zhipu_api_key or settings.zhipu_api_key == "your_api_key":
        print("❌ 错误: 智谱AI API密钥未配置")
        print("请在 .env 文件中设置 ZHIPU_API_KEY")
        sys.exit(1)
    
    # 显示启动信息
    print("🚀 启动文心小说后端服务")
    print("=" * 50)
    print(f"🌐 服务地址: http://{args.host}:{args.port}")
    print(f"📚 API文档: http://{args.host}:{args.port}/docs")
    print(f"🔧 健康检查: http://{args.host}:{args.port}/health")
    print(f"🔍 日志级别: {args.log_level}")
    print(f"🏗️ 运行环境: {args.env}")
    print(f"🤖 默认AI: {settings.default_ai_provider}")
    print("=" * 50)
    
    # 启动服务
    try:
        if args.env == 'prod':
            # 生产环境配置
            uvicorn.run(
                "main:app",
                host=args.host,
                port=args.port,
                workers=args.workers,
                log_level=args.log_level,
                access_log=True,
                reload=False
            )
        else:
            # 开发环境配置
            uvicorn.run(
                "main:app",
                host=args.host,
                port=args.port,
                reload=args.reload,
                log_level=args.log_level,
                access_log=True
            )
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 服务启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()