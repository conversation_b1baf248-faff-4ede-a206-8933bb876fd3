"""
🧪 [测试] 情感采矿机测试模块

测试情感采矿机的各项功能，包括：
1. 情感关键词匹配
2. 文本段落分割
3. 真实性过滤
4. 批量文件处理

作者: AI小速写团队
创建时间: 2025-08-02
"""

import pytest
import tempfile
import os
from pathlib import Path
from typing import List

from app.core.emotion_miner import (
    EmotionMiner, EmotionKeywordDict, TextSegmenter, 
    RealityFilter, EmotionSegment
)


class TestEmotionKeywordDict:
    """🎯 [测试] 情感关键词词典测试"""
    
    def test_init_keyword_dict(self):
        """测试情感词典初始化"""
        keyword_dict = EmotionKeywordDict()
        
        # 验证基本情感类别存在
        emotions = keyword_dict.get_all_emotions()
        assert "背叛" in emotions
        assert "狂喜" in emotions
        assert "愤怒" in emotions
        assert len(emotions) >= 5
        
    def test_get_emotion_keywords(self):
        """测试获取情感关键词"""
        keyword_dict = EmotionKeywordDict()
        
        # 测试背叛情感关键词
        betrayal_keywords = keyword_dict.get_emotion_keywords("背叛")
        assert "背叛" in betrayal_keywords
        assert "出卖" in betrayal_keywords
        assert len(betrayal_keywords) > 0
        
        # 测试不存在的情感
        unknown_keywords = keyword_dict.get_emotion_keywords("不存在的情感")
        assert unknown_keywords == []
        
    def test_physiological_keywords(self):
        """测试生理反应关键词"""
        keyword_dict = EmotionKeywordDict()
        
        physiological = keyword_dict.get_physiological_keywords()
        assert "心跳" in physiological
        assert "呼吸" in physiological
        assert "颤抖" in physiological
        assert len(physiological) > 10
        
    def test_sensory_keywords(self):
        """测试感官细节关键词"""
        keyword_dict = EmotionKeywordDict()
        
        sensory = keyword_dict.get_sensory_keywords()
        assert "看见" in sensory
        assert "听到" in sensory
        assert "触摸" in sensory
        assert len(sensory) > 10


class TestTextSegmenter:
    """📝 [测试] 文本分割器测试"""
    
    def test_segment_by_paragraphs(self):
        """测试段落分割"""
        segmenter = TextSegmenter()
        
        text = """这是第一个段落，包含一些内容。
        
这是第二个段落，也包含足够的内容来通过长度测试。

这是第三个段落。

短段落。

这是最后一个足够长的段落，应该被保留下来。"""
        
        paragraphs = segmenter.segment_by_paragraphs(text)
        
        # 验证分割结果
        assert len(paragraphs) >= 2  # 至少有2个有效段落
        for para in paragraphs:
            assert len(para) >= 20  # 每个段落至少20个字符
            
    def test_segment_by_sentences(self):
        """测试句子分割"""
        segmenter = TextSegmenter()
        
        text = "这是第一个句子。这是第二个句子！这是第三个句子？这是一个很长的句子，包含足够的内容来通过最小长度测试。短句。"
        
        sentences = segmenter.segment_by_sentences(text, min_length=20)
        
        # 验证分割结果
        assert len(sentences) >= 1
        for sentence in sentences:
            assert len(sentence) >= 20


class TestRealityFilter:
    """🎯 [测试] 真实性过滤器测试"""
    
    def setup_method(self):
        """设置测试环境"""
        self.keyword_dict = EmotionKeywordDict()
        self.reality_filter = RealityFilter(self.keyword_dict)
        
    def test_calculate_reality_score_high(self):
        """测试高真实性文本评分"""
        # 包含生理反应和感官细节的文本
        text = "他的心跳加速，呼吸急促，看见她的那一刻，感觉到血液在血管中沸腾。"
        
        score = self.reality_filter.calculate_reality_score(text)
        assert score > 0.7  # 应该是高分
        
    def test_calculate_reality_score_low(self):
        """测试低真实性文本评分"""
        # 包含大量修辞手法的文本
        text = "他的心情如同波涛汹涌的大海，仿佛被雷电击中，好像整个世界都在旋转。"
        
        score = self.reality_filter.calculate_reality_score(text)
        assert score < 0.5  # 应该是低分
        
    def test_extract_physiological_indicators(self):
        """测试提取生理反应指标"""
        text = "他的心跳加速，开始出汗，呼吸变得急促。"
        
        indicators = self.reality_filter.extract_physiological_indicators(text)
        assert "心跳" in indicators
        assert "出汗" in indicators
        assert "呼吸" in indicators
        
    def test_extract_sensory_details(self):
        """测试提取感官细节"""
        text = "他看见了她的眼神，听到了她的声音，感觉到了她的温暖。"
        
        details = self.reality_filter.extract_sensory_details(text)
        assert "看见" in details
        assert "听到" in details
        assert "感觉" in details


class TestEmotionMiner:
    """🧬 [测试] 情感采矿机测试"""
    
    def setup_method(self):
        """设置测试环境"""
        self.miner = EmotionMiner()
        
    def test_mine_emotions_from_text_basic(self):
        """测试基本情感挖掘功能"""
        text = """他感到被深深地背叛了，心跳加速，呼吸急促。
        
今天天气很好，阳光明媚。

她的背信弃义让他愤怒不已，血液在血管中沸腾，看见她就想到那个痛苦的夜晚。"""
        
        segments = self.miner.mine_emotions_from_text(text, target_emotions=["背叛", "愤怒"])
        
        # 验证结果
        assert len(segments) >= 1  # 至少找到一个情感段落
        
        # 检查第一个段落
        first_segment = segments[0]
        assert isinstance(first_segment, EmotionSegment)
        assert len(first_segment.emotion_keywords) > 0
        assert first_segment.confidence_score > 0
        assert first_segment.reality_score > 0
        
    def test_mine_emotions_with_reality_filter(self):
        """测试带真实性过滤的情感挖掘"""
        text = """他感到被背叛，心跳如鼓，仿佛整个世界都在旋转。
        
他被深深背叛了，心跳加速，呼吸急促，看见她就感到血液沸腾。"""
        
        # 使用较高的真实性阈值
        segments = self.miner.mine_emotions_from_text(text, target_emotions=["背叛"], min_reality_score=0.6)
        
        # 应该过滤掉修辞手法较多的段落
        for segment in segments:
            assert segment.reality_score >= 0.6
            
    def test_mine_emotions_from_file(self):
        """测试从文件挖掘情感"""
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
            f.write("""这是一个测试文件。

他感到被深深地背叛了，心跳加速，呼吸急促，看见她的眼神就想起那个痛苦的夜晚。

她的狂喜让整个房间都充满了欢声笑语，心脏跳动得如此之快。""")
            temp_file_path = f.name
        
        try:
            segments = self.miner.mine_emotions_from_file(temp_file_path, target_emotions=["背叛", "狂喜"])
            
            # 验证结果
            assert len(segments) >= 1
            
            # 验证文件来源信息
            for segment in segments:
                assert segment.source_info["source_type"] == "file"
                assert "file_path" in segment.source_info
                assert "file_name" in segment.source_info
                
        finally:
            # 清理临时文件
            os.unlink(temp_file_path)
            
    def test_get_mining_statistics(self):
        """测试挖掘统计功能"""
        # 创建一些测试段落
        segments = []
        
        # 创建背叛情感段落
        segment1 = EmotionSegment(
            text="测试文本1",
            emotion_keywords=["背叛"],
            confidence_score=0.8,
            reality_score=0.7,
            source_info={"emotion_type": "背叛"},
            physiological_indicators=["心跳"],
            sensory_details=["看见"]
        )
        
        # 创建狂喜情感段落
        segment2 = EmotionSegment(
            text="测试文本2",
            emotion_keywords=["狂喜"],
            confidence_score=0.9,
            reality_score=0.8,
            source_info={"emotion_type": "狂喜"},
            physiological_indicators=["心跳", "呼吸"],
            sensory_details=[]
        )
        
        segments = [segment1, segment2]
        
        stats = self.miner.get_mining_statistics(segments)
        
        # 验证统计结果
        assert stats["total_segments"] == 2
        assert stats["emotion_distribution"]["背叛"] == 1
        assert stats["emotion_distribution"]["狂喜"] == 1
        assert stats["average_reality_score"] == 0.75
        assert stats["average_confidence_score"] == 0.85
        assert stats["high_quality_segments"] == 2  # 两个都是高质量
        assert stats["physiological_segments"] == 2  # 两个都有生理指标
        assert stats["sensory_segments"] == 1  # 只有一个有感官细节
        
    def test_empty_statistics(self):
        """测试空列表的统计"""
        stats = self.miner.get_mining_statistics([])
        assert stats["total_segments"] == 0


class TestEmotionMinerIntegration:
    """🔄 [集成测试] 情感采矿机集成测试"""

    def test_full_mining_workflow(self):
        """测试完整的挖掘工作流"""
        miner = EmotionMiner()
        
        # 准备测试文本
        test_text = """第一章：背叛的痛苦

他从未想过，最信任的朋友会背叛他。当真相大白的那一刻，他的心跳停止了一拍，
呼吸变得急促，看见朋友的眼神时，感觉血液在血管中凝固。

第二章：狂喜的时刻

中奖的消息传来时，她狂喜不已，心脏跳动得如此之快，几乎要跳出胸膛。
她听到自己的尖叫声，看见周围人惊讶的表情，感觉整个世界都在为她庆祝。

第三章：平静的日常

今天是个普通的日子，阳光很好，微风轻拂。没有什么特别的事情发生。"""
        
        # 执行挖掘
        segments = miner.mine_emotions_from_text(
            text=test_text,
            target_emotions=["背叛", "狂喜"],
            min_reality_score=0.3
        )
        
        # 验证结果
        assert len(segments) >= 2  # 应该至少找到2个情感段落
        
        # 验证情感类型
        found_emotions = set()
        for segment in segments:
            emotion_type = segment.source_info.get("emotion_type")
            found_emotions.add(emotion_type)
            
            # 验证每个段落的基本属性
            assert len(segment.text) > 0
            assert len(segment.emotion_keywords) > 0
            assert 0 <= segment.confidence_score <= 1
            assert 0 <= segment.reality_score <= 1
        
        # 应该找到背叛和狂喜两种情感
        assert "背叛" in found_emotions or "狂喜" in found_emotions
        
        # 获取统计信息
        stats = miner.get_mining_statistics(segments)
        assert stats["total_segments"] == len(segments)
        assert stats["average_reality_score"] >= 0.3
