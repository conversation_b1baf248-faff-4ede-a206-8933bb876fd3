<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for app\core\config.py: 94%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>app\core\config.py</b>:
            <span class="pc_cov">94%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">48 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">45<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">3<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">0<span class="text"> excluded</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="z_adebc8a9b0574ea2___init___py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="z_1374716a89f3e08d___init___py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-02 12:57 +0800
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="pln"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t"><span class="str">&#127959;&#65039; [&#31995;&#32479;] &#24212;&#29992;&#26680;&#24515;&#37197;&#32622;&#27169;&#22359;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t"><span class="str">&#20351;&#29992;pydantic-settings&#31649;&#29702;&#29615;&#22659;&#21464;&#37327;&#21644;&#24212;&#29992;&#37197;&#32622;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t"><span class="key">from</span> <span class="nam">typing</span> <span class="key">import</span> <span class="nam">List</span><span class="op">,</span> <span class="nam">Optional</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t"><span class="key">from</span> <span class="nam">pydantic</span> <span class="key">import</span> <span class="nam">Field</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t"><span class="key">from</span> <span class="nam">pydantic_settings</span> <span class="key">import</span> <span class="nam">BaseSettings</span><span class="op">,</span> <span class="nam">SettingsConfigDict</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t"><span class="key">import</span> <span class="nam">structlog</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t"><span class="key">class</span> <span class="nam">Settings</span><span class="op">(</span><span class="nam">BaseSettings</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t">    <span class="str">"""&#24212;&#29992;&#37197;&#32622;&#31867;&#65292;&#20174;&#29615;&#22659;&#21464;&#37327;&#21152;&#36733;&#37197;&#32622;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t">    <span class="com"># &#27169;&#22411;&#37197;&#32622;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t">    <span class="nam">model_config</span> <span class="op">=</span> <span class="nam">SettingsConfigDict</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t">        <span class="nam">env_file</span><span class="op">=</span><span class="str">".env"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t">        <span class="nam">env_file_encoding</span><span class="op">=</span><span class="str">"utf-8"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t">        <span class="nam">case_sensitive</span><span class="op">=</span><span class="key">False</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t">        <span class="nam">extra</span><span class="op">=</span><span class="str">"ignore"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t23" href="#t23">23</a></span><span class="t">    <span class="com"># &#128272; [&#35748;&#35777;] &#26234;&#35889;AI&#37197;&#32622;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t24" href="#t24">24</a></span><span class="t">    <span class="nam">zhipu_api_key</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#26234;&#35889;AI API&#23494;&#38053;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t25" href="#t25">25</a></span><span class="t">    <span class="nam">zhipu_base_url</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t26" href="#t26">26</a></span><span class="t">        <span class="nam">default</span><span class="op">=</span><span class="str">"https://open.bigmodel.cn/api/paas/v4/"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t27" href="#t27">27</a></span><span class="t">        <span class="nam">description</span><span class="op">=</span><span class="str">"&#26234;&#35889;AI API&#22522;&#30784;URL"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t28" href="#t28">28</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t29" href="#t29">29</a></span><span class="t">    <span class="nam">zhipu_model</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="str">"glm-4.5"</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#26234;&#35889;AI&#40664;&#35748;&#27169;&#22411;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t30" href="#t30">30</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t31" href="#t31">31</a></span><span class="t">    <span class="com"># &#128272; [&#35748;&#35777;] Kimi AI&#37197;&#32622;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t32" href="#t32">32</a></span><span class="t">    <span class="nam">kimi_api_key</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Kimi AI API&#23494;&#38053;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t33" href="#t33">33</a></span><span class="t">    <span class="nam">kimi_base_url</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t34" href="#t34">34</a></span><span class="t">        <span class="nam">default</span><span class="op">=</span><span class="str">"https://api.moonshot.cn/v1"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t35" href="#t35">35</a></span><span class="t">        <span class="nam">description</span><span class="op">=</span><span class="str">"Kimi AI API&#22522;&#30784;URL"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t36" href="#t36">36</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t37" href="#t37">37</a></span><span class="t">    <span class="nam">kimi_model</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="str">"moonshot-v1-8k"</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Kimi AI&#40664;&#35748;&#27169;&#22411;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t38" href="#t38">38</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t39" href="#t39">39</a></span><span class="t">    <span class="com"># &#127912; [UI] &#40664;&#35748;AI&#25552;&#20379;&#21830;&#37197;&#32622;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t40" href="#t40">40</a></span><span class="t">    <span class="nam">default_ai_provider</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="str">"zhipu"</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#40664;&#35748;AI&#25552;&#20379;&#21830;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t41" href="#t41">41</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t42" href="#t42">42</a></span><span class="t">    <span class="com"># &#128190; [&#25968;&#25454;&#24211;] &#25968;&#25454;&#24211;&#37197;&#32622;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t43" href="#t43">43</a></span><span class="t">    <span class="nam">database_url</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t44" href="#t44">44</a></span><span class="t">        <span class="nam">default</span><span class="op">=</span><span class="str">"sqlite+aiosqlite:///./data/novel_generation.db"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t45" href="#t45">45</a></span><span class="t">        <span class="nam">description</span><span class="op">=</span><span class="str">"&#25968;&#25454;&#24211;&#36830;&#25509;URL"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t46" href="#t46">46</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t47" href="#t47">47</a></span><span class="t">    <span class="nam">database_echo</span><span class="op">:</span> <span class="nam">bool</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="key">False</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#26159;&#21542;&#36755;&#20986;SQL&#35843;&#35797;&#20449;&#24687;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t48" href="#t48">48</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t49" href="#t49">49</a></span><span class="t">    <span class="com"># &#127760; [API] FastAPI&#26381;&#21153;&#37197;&#32622;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t50" href="#t50">50</a></span><span class="t">    <span class="nam">api_host</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="str">"127.0.0.1"</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"API&#26381;&#21153;&#20027;&#26426;&#22320;&#22336;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t51" href="#t51">51</a></span><span class="t">    <span class="nam">api_port</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="num">8000</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"API&#26381;&#21153;&#31471;&#21475;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t52" href="#t52">52</a></span><span class="t">    <span class="nam">api_reload</span><span class="op">:</span> <span class="nam">bool</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#26159;&#21542;&#21551;&#29992;&#28909;&#37325;&#36733;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t53" href="#t53">53</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t54" href="#t54">54</a></span><span class="t">    <span class="com"># &#128221; [&#29983;&#25104;] &#26085;&#24535;&#37197;&#32622;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t55" href="#t55">55</a></span><span class="t">    <span class="nam">log_level</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="str">"INFO"</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#26085;&#24535;&#32423;&#21035;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t56" href="#t56">56</a></span><span class="t">    <span class="nam">log_format</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t57" href="#t57">57</a></span><span class="t">        <span class="nam">default</span><span class="op">=</span><span class="str">"%(asctime)s - %(name)s - %(levelname)s - %(message)s"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t58" href="#t58">58</a></span><span class="t">        <span class="nam">description</span><span class="op">=</span><span class="str">"&#26085;&#24535;&#26684;&#24335;"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t59" href="#t59">59</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t60" href="#t60">60</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t61" href="#t61">61</a></span><span class="t">    <span class="com"># &#127760; [API] CORS&#37197;&#32622;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t62" href="#t62">62</a></span><span class="t">    <span class="nam">allowed_origins</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t63" href="#t63">63</a></span><span class="t">        <span class="nam">default</span><span class="op">=</span><span class="str">"http://localhost:3000,http://127.0.0.1:3000,http://localhost:5173,http://127.0.0.1:5173"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t64" href="#t64">64</a></span><span class="t">        <span class="nam">description</span><span class="op">=</span><span class="str">"&#20801;&#35768;&#30340;&#36328;&#22495;&#28304;"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t65" href="#t65">65</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t66" href="#t66">66</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t67" href="#t67">67</a></span><span class="t">    <span class="op">@</span><span class="nam">property</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t68" href="#t68">68</a></span><span class="t">    <span class="key">def</span> <span class="nam">allowed_origins_list</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span> <span class="op">-></span> <span class="nam">List</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t69" href="#t69">69</a></span><span class="t">        <span class="str">"""&#23558;CORS&#28304;&#23383;&#31526;&#20018;&#36716;&#25442;&#20026;&#21015;&#34920;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t70" href="#t70">70</a></span><span class="t">        <span class="key">return</span> <span class="op">[</span><span class="nam">origin</span><span class="op">.</span><span class="nam">strip</span><span class="op">(</span><span class="op">)</span> <span class="key">for</span> <span class="nam">origin</span> <span class="key">in</span> <span class="nam">self</span><span class="op">.</span><span class="nam">allowed_origins</span><span class="op">.</span><span class="nam">split</span><span class="op">(</span><span class="str">","</span><span class="op">)</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t71" href="#t71">71</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t72" href="#t72">72</a></span><span class="t">    <span class="com"># &#128221; [&#29983;&#25104;] &#29983;&#25104;&#21442;&#25968;&#37197;&#32622;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t73" href="#t73">73</a></span><span class="t">    <span class="nam">default_temperature</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="num">0.8</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#40664;&#35748;&#28201;&#24230;&#21442;&#25968;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t74" href="#t74">74</a></span><span class="t">    <span class="nam">default_max_tokens</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="num">3000</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#40664;&#35748;&#26368;&#22823;token&#25968;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t75" href="#t75">75</a></span><span class="t">    <span class="nam">default_story_bible_tokens</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="num">3000</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#25925;&#20107;&#22307;&#32463;&#40664;&#35748;token&#25968;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t76" href="#t76">76</a></span><span class="t">    <span class="nam">default_chapter_tokens</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="num">4000</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#31456;&#33410;&#29983;&#25104;&#40664;&#35748;token&#25968;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t77" href="#t77">77</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t78" href="#t78">78</a></span><span class="t">    <span class="com"># &#128260; [&#20219;&#21153;] &#27969;&#24335;&#29983;&#25104;&#37197;&#32622;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t79" href="#t79">79</a></span><span class="t">    <span class="nam">stream_chunk_size</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="num">1024</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#27969;&#24335;&#29983;&#25104;&#20998;&#22359;&#22823;&#23567;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t80" href="#t80">80</a></span><span class="t">    <span class="nam">stream_timeout</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="num">60</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#27969;&#24335;&#29983;&#25104;&#36229;&#26102;&#26102;&#38388;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t81" href="#t81">81</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t82" href="#t82">82</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t83" href="#t83">83</a></span><span class="t"><span class="com"># &#20840;&#23616;&#37197;&#32622;&#23454;&#20363;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t84" href="#t84">84</a></span><span class="t"><span class="nam">settings</span> <span class="op">=</span> <span class="nam">Settings</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t85" href="#t85">85</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t86" href="#t86">86</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t87" href="#t87">87</a></span><span class="t"><span class="key">def</span> <span class="nam">setup_logging</span><span class="op">(</span><span class="op">)</span> <span class="op">-></span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t88" href="#t88">88</a></span><span class="t">    <span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t89" href="#t89">89</a></span><span class="t"><span class="str">    &#127959;&#65039; [&#31995;&#32479;] &#37197;&#32622;&#32467;&#26500;&#21270;&#20013;&#25991;&#26085;&#24535;&#31995;&#32479;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t90" href="#t90">90</a></span><span class="t"><span class="str">    &#20351;&#29992;structlog&#23454;&#29616;&#32467;&#26500;&#21270;&#26085;&#24535;&#35760;&#24405;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t91" href="#t91">91</a></span><span class="t"><span class="str">    """</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t92" href="#t92">92</a></span><span class="t">    <span class="nam">structlog</span><span class="op">.</span><span class="nam">configure</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t93" href="#t93">93</a></span><span class="t">        <span class="nam">processors</span><span class="op">=</span><span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t94" href="#t94">94</a></span><span class="t">            <span class="nam">structlog</span><span class="op">.</span><span class="nam">stdlib</span><span class="op">.</span><span class="nam">filter_by_level</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t95" href="#t95">95</a></span><span class="t">            <span class="nam">structlog</span><span class="op">.</span><span class="nam">stdlib</span><span class="op">.</span><span class="nam">add_logger_name</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t96" href="#t96">96</a></span><span class="t">            <span class="nam">structlog</span><span class="op">.</span><span class="nam">stdlib</span><span class="op">.</span><span class="nam">add_log_level</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t97" href="#t97">97</a></span><span class="t">            <span class="nam">structlog</span><span class="op">.</span><span class="nam">stdlib</span><span class="op">.</span><span class="nam">PositionalArgumentsFormatter</span><span class="op">(</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t98" href="#t98">98</a></span><span class="t">            <span class="nam">structlog</span><span class="op">.</span><span class="nam">processors</span><span class="op">.</span><span class="nam">TimeStamper</span><span class="op">(</span><span class="nam">fmt</span><span class="op">=</span><span class="str">"%Y-%m-%d %H:%M:%S"</span><span class="op">,</span> <span class="nam">utc</span><span class="op">=</span><span class="key">False</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t99" href="#t99">99</a></span><span class="t">            <span class="nam">structlog</span><span class="op">.</span><span class="nam">processors</span><span class="op">.</span><span class="nam">add_log_level</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t100" href="#t100">100</a></span><span class="t">            <span class="nam">structlog</span><span class="op">.</span><span class="nam">processors</span><span class="op">.</span><span class="nam">JSONRenderer</span><span class="op">(</span><span class="nam">ensure_ascii</span><span class="op">=</span><span class="key">False</span><span class="op">,</span> <span class="nam">sort_keys</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t101" href="#t101">101</a></span><span class="t">        <span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t102" href="#t102">102</a></span><span class="t">        <span class="nam">context_class</span><span class="op">=</span><span class="nam">dict</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t103" href="#t103">103</a></span><span class="t">        <span class="nam">logger_factory</span><span class="op">=</span><span class="nam">structlog</span><span class="op">.</span><span class="nam">stdlib</span><span class="op">.</span><span class="nam">LoggerFactory</span><span class="op">(</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t104" href="#t104">104</a></span><span class="t">        <span class="nam">wrapper_class</span><span class="op">=</span><span class="nam">structlog</span><span class="op">.</span><span class="nam">stdlib</span><span class="op">.</span><span class="nam">BoundLogger</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t105" href="#t105">105</a></span><span class="t">        <span class="nam">cache_logger_on_first_use</span><span class="op">=</span><span class="key">True</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t106" href="#t106">106</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t107" href="#t107">107</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t108" href="#t108">108</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t109" href="#t109">109</a></span><span class="t"><span class="key">def</span> <span class="nam">log_info</span><span class="op">(</span><span class="nam">category</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span> <span class="nam">message</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span> <span class="op">**</span><span class="nam">kwargs</span><span class="op">)</span> <span class="op">-></span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t110" href="#t110">110</a></span><span class="t">    <span class="str">"""&#35760;&#24405;&#20449;&#24687;&#32423;&#21035;&#30340;&#20013;&#25991;&#26085;&#24535;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t111" href="#t111">111</a></span><span class="t">    <span class="nam">logger</span> <span class="op">=</span> <span class="nam">structlog</span><span class="op">.</span><span class="nam">get_logger</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t112" href="#t112">112</a></span><span class="t">    <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="nam">message</span><span class="op">,</span> <span class="nam">&#20998;&#31867;</span><span class="op">=</span><span class="fst">f"</span><span class="fst">&#127919;</span><span class="op">{</span><span class="nam">category</span><span class="op">}</span><span class="fst">"</span><span class="op">,</span> <span class="op">**</span><span class="nam">kwargs</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t113" href="#t113">113</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t114" href="#t114">114</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t115" href="#t115">115</a></span><span class="t"><span class="key">def</span> <span class="nam">log_error</span><span class="op">(</span><span class="nam">category</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span> <span class="nam">message</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span> <span class="nam">error</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">Exception</span><span class="op">]</span> <span class="op">=</span> <span class="key">None</span><span class="op">,</span> <span class="op">**</span><span class="nam">kwargs</span><span class="op">)</span> <span class="op">-></span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t116" href="#t116">116</a></span><span class="t">    <span class="str">"""&#35760;&#24405;&#38169;&#35823;&#32423;&#21035;&#30340;&#20013;&#25991;&#26085;&#24535;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t117" href="#t117">117</a></span><span class="t">    <span class="nam">logger</span> <span class="op">=</span> <span class="nam">structlog</span><span class="op">.</span><span class="nam">get_logger</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t118" href="#t118">118</a></span><span class="t">    <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t119" href="#t119">119</a></span><span class="t">        <span class="nam">message</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t120" href="#t120">120</a></span><span class="t">        <span class="nam">&#20998;&#31867;</span><span class="op">=</span><span class="fst">f"</span><span class="fst">&#10060;</span><span class="op">{</span><span class="nam">category</span><span class="op">}</span><span class="fst">"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t121" href="#t121">121</a></span><span class="t">        <span class="nam">&#38169;&#35823;</span><span class="op">=</span><span class="nam">str</span><span class="op">(</span><span class="nam">error</span><span class="op">)</span> <span class="key">if</span> <span class="nam">error</span> <span class="key">else</span> <span class="key">None</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t122" href="#t122">122</a></span><span class="t">        <span class="op">**</span><span class="nam">kwargs</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t123" href="#t123">123</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t124" href="#t124">124</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t125" href="#t125">125</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t126" href="#t126">126</a></span><span class="t"><span class="key">def</span> <span class="nam">log_debug</span><span class="op">(</span><span class="nam">category</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span> <span class="nam">message</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span> <span class="op">**</span><span class="nam">kwargs</span><span class="op">)</span> <span class="op">-></span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t127" href="#t127">127</a></span><span class="t">    <span class="str">"""&#35760;&#24405;&#35843;&#35797;&#32423;&#21035;&#30340;&#20013;&#25991;&#26085;&#24535;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t128" href="#t128">128</a></span><span class="t">    <span class="nam">logger</span> <span class="op">=</span> <span class="nam">structlog</span><span class="op">.</span><span class="nam">get_logger</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t129" href="#t129">129</a></span><span class="t">    <span class="nam">logger</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="nam">message</span><span class="op">,</span> <span class="nam">&#20998;&#31867;</span><span class="op">=</span><span class="fst">f"</span><span class="fst">&#128295;</span><span class="op">{</span><span class="nam">category</span><span class="op">}</span><span class="fst">"</span><span class="op">,</span> <span class="op">**</span><span class="nam">kwargs</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t130" href="#t130">130</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t131" href="#t131">131</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t132" href="#t132">132</a></span><span class="t"><span class="key">def</span> <span class="nam">log_warning</span><span class="op">(</span><span class="nam">category</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span> <span class="nam">message</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span> <span class="op">**</span><span class="nam">kwargs</span><span class="op">)</span> <span class="op">-></span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t133" href="#t133">133</a></span><span class="t">    <span class="str">"""&#35760;&#24405;&#35686;&#21578;&#32423;&#21035;&#30340;&#20013;&#25991;&#26085;&#24535;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t134" href="#t134">134</a></span><span class="t">    <span class="nam">logger</span> <span class="op">=</span> <span class="nam">structlog</span><span class="op">.</span><span class="nam">get_logger</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t135" href="#t135">135</a></span><span class="t">    <span class="nam">logger</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="nam">message</span><span class="op">,</span> <span class="nam">&#20998;&#31867;</span><span class="op">=</span><span class="fst">f"</span><span class="fst">&#9888;&#65039;</span><span class="op">{</span><span class="nam">category</span><span class="op">}</span><span class="fst">"</span><span class="op">,</span> <span class="op">**</span><span class="nam">kwargs</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t136" href="#t136">136</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t137" href="#t137">137</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t138" href="#t138">138</a></span><span class="t"><span class="key">def</span> <span class="nam">log_success</span><span class="op">(</span><span class="nam">category</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span> <span class="nam">message</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span> <span class="op">**</span><span class="nam">kwargs</span><span class="op">)</span> <span class="op">-></span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t139" href="#t139">139</a></span><span class="t">    <span class="str">"""&#35760;&#24405;&#25104;&#21151;&#25805;&#20316;&#30340;&#20013;&#25991;&#26085;&#24535;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t140" href="#t140">140</a></span><span class="t">    <span class="nam">logger</span> <span class="op">=</span> <span class="nam">structlog</span><span class="op">.</span><span class="nam">get_logger</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t141" href="#t141">141</a></span><span class="t">    <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="nam">message</span><span class="op">,</span> <span class="nam">&#20998;&#31867;</span><span class="op">=</span><span class="fst">f"</span><span class="fst">&#9989;</span><span class="op">{</span><span class="nam">category</span><span class="op">}</span><span class="fst">"</span><span class="op">,</span> <span class="op">**</span><span class="nam">kwargs</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="z_adebc8a9b0574ea2___init___py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="z_1374716a89f3e08d___init___py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-02 12:57 +0800
        </p>
    </div>
</footer>
</body>
</html>
