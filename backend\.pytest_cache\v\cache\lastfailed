{"tests/test_database.py::TestDatabaseManager::test_database_session_creation": true, "tests/test_database.py::TestDatabaseManager::test_database_table_creation": true, "tests/test_database.py::TestDatabaseManager::test_database_model_operations": true, "tests/repositories/test_bible_repo.py::TestStoryBibleRepository::test_update_bible_status": true, "tests/repositories/test_bible_repo.py::TestStoryBibleRepository::test_list_bibles": true, "tests/repositories/test_bible_repo.py::TestChapterRepository::test_update_chapter_status": true, "tests/routers/test_persistence_routes.py": true, "tests/routers/test_persistence_routes.py::TestPersistenceRoutes::test_get_story_bible": true, "tests/core/test_dna_compiler.py::TestShellRemover": true, "tests/core/test_dna_compiler.py::TestDehydrator": true, "tests/core/test_dna_compiler.py::TestGenePairer": true, "tests/core/test_dna_compiler.py::TestEntropyGenerator": true, "tests/core/test_dna_compiler.py::TestDNACompiler": true, "tests/core/test_dna_compiler.py::TestEmotionalDNA": true, "tests/core/test_dna_compiler.py::TestDNACompilerIntegration": true}