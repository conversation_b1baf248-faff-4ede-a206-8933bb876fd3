# 文心小说后端服务环境配置示例
# 复制此文件为 .env 并修改相应配置

# ========================================
# 智谱AI配置 (GLM-4.5)
# ========================================
# 从 https://open.bigmodel.cn 获取API密钥
ZHIPU_API_KEY=your_zhipu_api_key_here
ZHIPU_BASE_URL=https://open.bigmodel.cn/api/paas/v4/
ZHIPU_MODEL=glm-4.5

# ========================================
# Kimi AI配置 (月之暗面)
# ========================================
# 从 https://platform.moonshot.cn 获取API密钥
KIMI_API_KEY=your_kimi_api_key_here
KIMI_BASE_URL=https://api.moonshot.cn/v1
KIMI_MODEL=moonshot-v1-8k

# ========================================
# 默认AI提供商配置
# ========================================
# 可选值: zhipu, kimi
DEFAULT_AI_PROVIDER=zhipu

# ========================================
# 数据库配置
# ========================================
DATABASE_URL=sqlite+aiosqlite:///./data/novel_generation.db
DATABASE_ECHO=false

# ========================================
# FastAPI服务配置
# ========================================
API_HOST=127.0.0.1
API_PORT=8000
API_RELOAD=true

# ========================================
# 日志配置
# ========================================
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# ========================================
# CORS配置
# ========================================
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://localhost:5173,http://127.0.0.1:5173

# ========================================
# 生成参数配置
# ========================================
DEFAULT_TEMPERATURE=0.8
DEFAULT_MAX_TOKENS=3000
DEFAULT_STORY_BIBLE_TOKENS=3000
DEFAULT_CHAPTER_TOKENS=4000

# ========================================
# 流式生成配置
# ========================================
STREAM_CHUNK_SIZE=1024
STREAM_TIMEOUT=60