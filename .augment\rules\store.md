---
type: "always_apply"
---

# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🗣️ 交流规范
- 所有交流都是用中文
- 所有代码都需要中文注释中文日志
- 解决问题的时候一个一个去解决，不要采用精简的办法，要一步一步的去解决问题，不能一步到位，解决一个验证一个

## 📋 项目架构

### 前后端分离架构
- **前端**: React 19 + TypeScript + Vite + Tailwind CSS (位于 `frontend/`)
- **后端**: Python + FastAPI (位于 `backend/`，注意：后端目录当前只有文档)
- **数据管理**: 使用 uv 作为 Python 包管理器
- **AI服务**: 支持智谱AI和Kimi的多AI服务架构

### 前端架构特色
- **组件系统**: 基于原子设计的UI组件库 (`frontend/src/components/ui/`)
- **主题系统**: 全局主题提供器，支持暗黑模式和字体大小调节 (`ThemeProvider.tsx`)
- **状态管理**: 使用 Zustand 进行轻量级状态管理 (`frontend/src/stores/`)
- **路由系统**: React Router DOM v7 实现页面路由
- **页面结构**: 
  - `bible/` - 故事圣经页面
  - `cockpit/` - 创作驾驶舱，包含AI面板、编辑器、故事大纲
  - `generating/` - 生成页面
  - `home/` - 首页
  - `prompt/` - 提示词页面
  - `reading/` - 阅读页面

## 🛠️ 开发规范

### 代码规范
- 不要使用模拟数据
- 开发阶段不需要向后向前兼容
- 所有请求都需要中文日志，前后端都是，所有数据流流通的地方都需要中文日志
- 中文日志的格式需要保持良好的格式增加可读性，最好实现结构化的中文日志

### 模块化开发规范（遵循kiss原则）
- **文件大小限制**: 每个代码文件不能超过500行
- **目录结构**: 保持简洁可维护的目录结构
- **单一职责**: 每个模块只负责一个明确的功能
- **文件拆分原则**:
  - 当文件接近500行时，按功能拆分成多个子模块
  - 复杂组件拆分为主组件 + 子组件
  - 工具函数按类别分组到不同文件
  - API服务按业务领域分模块
- **模块导出**: 使用 `index.ts` 文件统一导出模块接口
- **命名规范**: 文件名和目录名使用小驼峰或kebab-case，保持一致性

### 中文日志系统规范
- **日志格式**: 统一使用结构化的中文日志格式，包含时间戳、分类、emoji表情和详细信息
- **日志分类与Emoji标识**:
  - `🔐 [认证]` - 用户登录/登出/token验证
  - `📝 [生成]` - AI内容生成相关
  - `🎨 [UI]` - 界面交互和主题切换
  - `🏗️ [系统]` - 应用初始化和架构相关
  - `📁 [文件]` - 文件操作和项目管理
  - `🔧 [调试]` - 开发调试信息
  - `⚠️ [警告]` - 警告信息
  - `❌ [错误]` - 错误信息
  - `✅ [成功]` - 成功操作
  - `🌐 [API]` - 网络请求和API调用

#### 日志函数参数规范:
```typescript
// 基础日志函数 (位于 config/env.ts)
debugLog(category: string, message: string, ...args: any[])

// 参数说明:
// category: 日志分类 (如: "认证", "生成", "UI", "系统")
// message: 中文描述信息
// args: 可选的额外参数对象或数据

// 使用示例:
debugLog('认证', '用户登录成功', { userId: 123, email: '<EMAIL>' });
debugLog('生成', '开始生成章节内容', { chapterId: 5, wordCount: 1000 });
debugLog('UI', '切换到暗黑模式', { previousMode: 'light' });
```

#### 推荐的日志记录位置:
- **数据流入口/出口**: API请求开始和结束
- **状态变更**: Store状态更新
- **用户交互**: 按钮点击、页面导航
- **错误处理**: catch块和失败回调
- **性能关键点**: 加载开始/结束、渲染完成

#### 后端Python日志系统规范:
```python
import logging
import structlog
from datetime import datetime

# 推荐使用 structlog 实现结构化中文日志
# 日志配置函数
def setup_chinese_logger():
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.add_log_level,
            structlog.processors.JSONRenderer(ensure_ascii=False)
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )

# 日志函数参数规范
logger = structlog.get_logger()

# 使用方式:
def log_info(category: str, message: str, **kwargs):
    """记录信息日志"""
    logger.info(message, 分类=category, **kwargs)

def log_error(category: str, message: str, error: Exception = None, **kwargs):
    """记录错误日志"""
    logger.error(message, 分类=category, 错误=str(error) if error else None, **kwargs)

def log_debug(category: str, message: str, **kwargs):
    """记录调试日志"""
    logger.debug(message, 分类=category, **kwargs)

# 使用示例:
log_info("🔐认证", "用户登录成功", 用户ID=123, 邮箱="<EMAIL>")
log_info("📝生成", "开始生成章节内容", 章节ID=5, 字数=1000, AI模型="智谱GLM-4")
log_error("🌐API", "智谱AI调用失败", error=exception_obj, 重试次数=3)
log_debug("🎨UI", "主题切换完成", 之前模式="light", 当前模式="dark")
```

#### 后端日志分类标准:
- `🔐 [认证]` - 用户认证、Token验证、权限检查
- `📝 [生成]` - AI内容生成、提示词处理
- `🌐 [API]` - 外部API调用（智谱AI、Kimi等）
- `💾 [数据库]` - 数据库操作、查询、迁移
- `🏗️ [系统]` - 应用启动、配置加载
- `⚡ [性能]` - 响应时间、资源使用情况
- `🔄 [任务]` - 异步任务、队列处理
- `📁 [文件]` - 文件操作、存储管理
- `🛡️ [安全]` - 安全相关操作和检查

### UI开发规范
- 所有创建的UI都需要使用我们的主题系统
- 在写UI之前都需要看一下我们的主题系统是怎么实现的
- 主题系统位于 `frontend/src/components/ThemeProvider.tsx`
- 支持暗黑模式切换和字体大小调节

### 开发环境
- 不用启动前后端，我们的前后端支持热重载且开发的时候我已经启动
- 前端开发服务器: `npm run dev` (http://localhost:3000)
- 前端类型检查: TypeScript 严格模式
- 前端代码检查: ESLint

## 📚 重要文档位置
- 智谱AI文档: `E:\Rust-store\backend\zp/` (包含完整的API文档)
- 开发计划文档: `E:\Rust-store\后端开发计划文档.md`
- Python环境规范: `E:\Rust-store\docs\Python开发环境与依赖管理规范.md`

## ✅ 任务管理
- 在开发的时候完成了任务记得查看 `E:\Rust-store\AI小说工作流开发计划 (本地验证版).md` 更新相应这个文档的任务状态

## 🔧 依赖管理
- 我们是虚拟环境，使用的是 uv 管理
- 在遇到反复不能解决的依赖冲突版本问题、库的问题等的时候需要擅长用搜索网站找到解决的办法

## 🎯 常用命令

### 前端开发
```bash
cd frontend
npm run dev          # 启动开发服务器
npm run build        # 构建生产版本
npm run lint         # 代码检查
```

### 代码质量检查
```bash
# 检查文件行数（超过500行的文件）
find frontend/src -name "*.tsx" -o -name "*.ts" | xargs wc -l | awk '$1 > 500 {print $2 " 有 " $1 " 行，超过500行限制"}'

# 检查所有TypeScript文件的行数
find frontend/src -name "*.tsx" -o -name "*.ts" | xargs wc -l | sort -nr
```

### 后端开发 (Python + uv)
```bash
cd backend
uv sync              # 同步依赖
uv run pytest       # 运行测试
uv run uvicorn main:app --reload  # 启动开发服务器
```

### 日志工具命令
```bash
# 实时查看后端日志（结构化JSON格式）
tail -f backend/logs/app.log | jq '.'

# 按分类筛选日志
tail -f backend/logs/app.log | jq 'select(.分类 == "🔐认证")'

# 查看错误日志
tail -f backend/logs/app.log | jq 'select(.level == "error")'

# 前端开发工具 - 检查日志使用情况
grep -r "console.log\|debugLog" frontend/src --include="*.ts" --include="*.tsx"
```
