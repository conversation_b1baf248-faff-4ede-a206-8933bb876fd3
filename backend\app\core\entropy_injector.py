"""
🌀 [熵增] 熵增扰动器模块

实现全方位的去AI化处理，通过多种扰动技术破坏AI文本的完美主义特征，
让生成的内容更具人类真实感和不完美性。

作者: 文心小说后端服务系统
创建时间: 2025-08-02
"""

import re
import random
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from app.core.config import log_info, log_debug, log_error


class DisruptionLevel(Enum):
    """扰动级别枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"


@dataclass
class DisruptionResult:
    """扰动处理结果数据类"""
    original_text: str
    processed_text: str
    modifications_applied: int
    disruption_techniques: List[str]
    ai_trace_reduction: float  # AI痕迹降低百分比
    processing_details: Dict[str, Any]


class AITraceAnalyzer:
    """🤖 AI痕迹分析器 - 评估文本的完美主义程度"""
    
    def __init__(self):
        # AI文本特征模式
        self.ai_patterns = {
            "完美句式": [
                r"不仅.*而且.*",
                r"既.*又.*",
                r"一方面.*另一方面.*",
                r"首先.*其次.*最后.*"
            ],
            "过度修饰": [
                r"[的地得]{2,}",
                r"非常.*非常.*",
                r"十分.*十分.*",
                r"极其.*极其.*"
            ],
            "机械对话": [
                r'".*".*说道',
                r'".*".*回答',
                r'".*".*表示',
                r'".*".*解释'
            ],
            "完美描写": [
                r"完美的.*",
                r"无瑕的.*",
                r"绝对的.*",
                r"毫无疑问.*"
            ]
        }
        
        log_debug("熵增扰动", "AI痕迹分析器初始化完成", 模式类型数=len(self.ai_patterns))
    
    def analyze_ai_traces(self, text: str) -> Dict[str, Any]:
        """
        🔍 分析文本中的AI痕迹
        
        Args:
            text: 输入文本
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        log_debug("熵增扰动", "开始分析AI痕迹", 文本长度=len(text))
        
        total_matches = 0
        pattern_matches = {}
        
        for category, patterns in self.ai_patterns.items():
            matches = 0
            for pattern in patterns:
                matches += len(re.findall(pattern, text))
            pattern_matches[category] = matches
            total_matches += matches
        
        # 计算AI痕迹百分比（基于文本长度和匹配数量）
        text_length = len(text)
        if text_length == 0:
            ai_percentage = 0.0
        else:
            # 每100字符中的AI特征数量
            ai_percentage = min(100.0, (total_matches / text_length) * 100 * 10)
        
        result = {
            "ai_percentage": ai_percentage,
            "total_matches": total_matches,
            "pattern_matches": pattern_matches,
            "text_length": text_length,
            "assessment": self._assess_ai_level(ai_percentage)
        }
        
        log_info("熵增扰动", "AI痕迹分析完成", 
                AI百分比=f"{ai_percentage:.1f}%",
                总匹配数=total_matches,
                评估等级=result["assessment"])
        
        return result
    
    def _assess_ai_level(self, ai_percentage: float) -> str:
        """评估AI痕迹等级"""
        if ai_percentage >= 80:
            return "极高AI痕迹"
        elif ai_percentage >= 60:
            return "高AI痕迹"
        elif ai_percentage >= 40:
            return "中等AI痕迹"
        elif ai_percentage >= 20:
            return "低AI痕迹"
        else:
            return "自然文本"


class PerfectionBreaker:
    """💥 完美意象破坏器 - 破坏过于工整的比喻和描写"""
    
    def __init__(self):
        # 不确定词汇
        self.uncertainty_words = [
            "似乎", "好像", "仿佛", "大概", "可能", "或许", "也许", "大约",
            "差不多", "几乎", "有点", "稍微", "略微", "隐约", "模糊地"
        ]
        
        # 破坏性词汇
        self.disruption_words = [
            "但是", "不过", "然而", "只是", "却", "倒是", "反而", "偏偏"
        ]
        
        log_debug("熵增扰动", "完美意象破坏器初始化完成")
    
    def break_perfect_metaphors(self, text: str) -> Tuple[str, int]:
        """
        💥 破坏完美比喻
        
        Args:
            text: 输入文本
            
        Returns:
            Tuple[str, int]: 处理后的文本和修改次数
        """
        log_debug("熵增扰动", "开始破坏完美比喻", 文本长度=len(text))
        
        modifications = 0
        result_text = text
        
        # 识别比喻句模式
        metaphor_patterns = [
            r"(.*像.*一样.*)",
            r"(.*如同.*般.*)",
            r"(.*宛如.*)",
            r"(.*犹如.*)"
        ]
        
        for pattern in metaphor_patterns:
            matches = re.finditer(pattern, result_text)
            for match in matches:
                original = match.group(1)
                
                # 随机添加不确定词
                if random.random() < 0.6:  # 60%概率
                    uncertainty = random.choice(self.uncertainty_words)
                    modified = original.replace("像", f"{uncertainty}像", 1)
                    modified = modified.replace("如同", f"{uncertainty}如同", 1)
                    modified = modified.replace("宛如", f"{uncertainty}宛如", 1)
                    modified = modified.replace("犹如", f"{uncertainty}犹如", 1)
                    
                    result_text = result_text.replace(original, modified, 1)
                    modifications += 1
                    log_debug("熵增扰动", "破坏比喻句", 原文=original[:20], 修改后=modified[:20])
        
        log_info("熵增扰动", "完美比喻破坏完成", 修改次数=modifications)
        return result_text, modifications


class EnvironmentNoiseInjector:
    """🌍 环境噪音注入器 - 在关键场景中插入不相关的环境细节"""
    
    def __init__(self):
        # 环境噪音库
        self.noise_library = {
            "声音": [
                "远处传来汽车鸣笛声", "楼上有人在拖拽家具", "空调嗡嗡作响",
                "窗外有鸟儿啁啾", "隔壁传来电视声", "走廊里有脚步声",
                "厨房里水龙头在滴水", "手机震动了一下", "时钟滴答作响"
            ],
            "视觉": [
                "墙上的影子在摇摆", "桌上的杯子里还有半杯水", "窗帘被风吹动",
                "地上有几片落叶", "角落里积了些灰尘", "玻璃上有水珠滑落",
                "阳光透过百叶窗洒下条纹", "远处有人在晾衣服", "猫咪从窗台跳下"
            ],
            "触觉": [
                "空气中有些闷热", "微风轻抚过脸颊", "衣服有点紧",
                "椅子有些硬", "手心微微出汗", "脚有点麻",
                "头发被风吹乱", "皮肤感到干燥", "鞋子有点挤脚"
            ],
            "嗅觉": [
                "空气中有淡淡的咖啡香", "闻到了洗衣粉的味道", "有股淡淡的花香",
                "空气中有些潮湿的味道", "闻到了食物的香味", "有股清洁剂的味道",
                "空气中有烟味", "闻到了香水味", "有股汽油味飘过"
            ]
        }
        
        log_debug("熵增扰动", "环境噪音注入器初始化完成", 噪音类型数=len(self.noise_library))
    
    def inject_environmental_noise(self, text: str, intensity: float = 0.3) -> Tuple[str, int]:
        """
        🌍 注入环境噪音
        
        Args:
            text: 输入文本
            intensity: 注入强度 (0.0-1.0)
            
        Returns:
            Tuple[str, int]: 处理后的文本和修改次数
        """
        log_debug("熵增扰动", "开始注入环境噪音", 文本长度=len(text), 强度=intensity)
        
        sentences = re.split(r'[。！？]', text)
        if not sentences:
            return text, 0
        
        modifications = 0
        result_sentences = []
        
        for sentence in sentences:
            if not sentence.strip():
                result_sentences.append(sentence)
                continue
            
            # 根据强度决定是否注入噪音
            if random.random() < intensity:
                # 随机选择噪音类型和内容
                noise_type = random.choice(list(self.noise_library.keys()))
                noise = random.choice(self.noise_library[noise_type])
                
                # 随机选择插入位置
                if random.random() < 0.5:
                    # 句子开头
                    modified_sentence = f"{noise}，{sentence.strip()}"
                else:
                    # 句子中间或结尾
                    words = sentence.strip().split()
                    if len(words) > 3:
                        insert_pos = random.randint(1, len(words) - 1)
                        words.insert(insert_pos, f"——{noise}——")
                        modified_sentence = "".join(words)
                    else:
                        modified_sentence = f"{sentence.strip()}，{noise}"
                
                result_sentences.append(modified_sentence)
                modifications += 1
                log_debug("熵增扰动", "注入环境噪音", 噪音类型=noise_type, 噪音内容=noise[:10])
            else:
                result_sentences.append(sentence)
        
        result_text = "。".join([s for s in result_sentences if s.strip()])
        if result_text and not result_text.endswith(('。', '！', '？')):
            result_text += "。"
        
        log_info("熵增扰动", "环境噪音注入完成", 修改次数=modifications)
        return result_text, modifications


class MicroDetailAdder:
    """🔬 微观细节添加器 - 为情感描写添加真实的身体微反应"""
    
    def __init__(self):
        # 微观身体反应库
        self.micro_reactions = {
            "紧张": [
                "不自觉地咬了咬嘴唇", "手指轻敲桌面", "眼皮跳了一下",
                "喉咙有些干涩", "手心微微出汗", "呼吸变得急促"
            ],
            "愤怒": [
                "太阳穴突突跳动", "握紧了拳头又松开", "牙齿咬得咯咯响",
                "脸颊微微发烫", "眼角抽搐了一下", "鼻翼轻微张开"
            ],
            "悲伤": [
                "眼眶有些湿润", "鼻子有点酸", "胸口闷闷的",
                "喉咙哽咽", "肩膀微微颤抖", "眼皮沉重"
            ],
            "喜悦": [
                "嘴角不由自主上扬", "眼睛眯成了月牙", "心跳加快了些",
                "脸颊微微发热", "手舞足蹈", "忍不住轻哼"
            ],
            "恐惧": [
                "汗毛竖起", "后背发凉", "腿有些发软",
                "心跳如鼓", "手微微颤抖", "瞳孔放大"
            ]
        }
        
        log_debug("熵增扰动", "微观细节添加器初始化完成", 情感类型数=len(self.micro_reactions))
    
    def add_micro_details(self, text: str, emotion_context: Optional[str] = None) -> Tuple[str, int]:
        """
        🔬 添加微观细节
        
        Args:
            text: 输入文本
            emotion_context: 情感上下文提示
            
        Returns:
            Tuple[str, int]: 处理后的文本和修改次数
        """
        log_debug("熵增扰动", "开始添加微观细节", 文本长度=len(text), 情感上下文=emotion_context)
        
        modifications = 0
        result_text = text
        
        # 如果有情感上下文，优先使用对应的微反应
        if emotion_context and emotion_context in self.micro_reactions:
            reactions = self.micro_reactions[emotion_context]
        else:
            # 随机选择情感类型
            emotion_type = random.choice(list(self.micro_reactions.keys()))
            reactions = self.micro_reactions[emotion_type]
        
        # 在适当位置插入微反应
        sentences = re.split(r'[。！？]', result_text)
        if len(sentences) > 1:
            # 选择1-2个位置插入微反应
            insert_count = min(2, max(1, len(sentences) // 3))
            insert_positions = random.sample(range(len(sentences) - 1), insert_count)
            
            for pos in sorted(insert_positions, reverse=True):
                if sentences[pos].strip():
                    reaction = random.choice(reactions)
                    sentences[pos] = f"{sentences[pos].strip()}，{reaction}"
                    modifications += 1
                    log_debug("熵增扰动", "添加微观细节", 位置=pos, 细节=reaction[:10])
        
        result_text = "。".join([s for s in sentences if s.strip()])
        if result_text and not result_text.endswith(('。', '！', '？')):
            result_text += "。"
        
        log_info("熵增扰动", "微观细节添加完成", 修改次数=modifications)
        return result_text, modifications


class ThoughtInterruptor:
    """🧠 思维中断注入器 - 打破过于流畅的思考过程"""

    def __init__(self):
        # 思维中断词汇
        self.interruption_words = [
            "等等", "不对", "算了", "嗯", "呃", "这个", "那个",
            "怎么说呢", "让我想想", "话说回来", "对了", "忘了说"
        ]

        # 思维跳跃词汇
        self.jump_words = [
            "突然想到", "对了", "说起来", "顺便说一下", "忽然",
            "刚想起", "差点忘了", "哦对", "还有"
        ]

        log_debug("熵增扰动", "思维中断注入器初始化完成")

    def inject_thought_interruptions(self, text: str, intensity: float = 0.2) -> Tuple[str, int]:
        """
        🧠 注入思维中断

        Args:
            text: 输入文本
            intensity: 中断强度 (0.0-1.0)

        Returns:
            Tuple[str, int]: 处理后的文本和修改次数
        """
        log_debug("熵增扰动", "开始注入思维中断", 文本长度=len(text), 强度=intensity)

        modifications = 0
        result_text = text

        # 查找思考相关的句子
        thinking_patterns = [
            r"(.*想.*)",
            r"(.*觉得.*)",
            r"(.*认为.*)",
            r"(.*感觉.*)",
            r"(.*以为.*)"
        ]

        for pattern in thinking_patterns:
            matches = list(re.finditer(pattern, result_text))
            for match in matches:
                if random.random() < intensity:
                    original = match.group(1)

                    # 随机选择中断类型
                    if random.random() < 0.5:
                        # 添加中断词
                        interrupt_word = random.choice(self.interruption_words)
                        modified = original.replace("想", f"想——{interrupt_word}——", 1)
                        modified = modified.replace("觉得", f"觉得——{interrupt_word}——", 1)
                    else:
                        # 添加思维跳跃
                        jump_word = random.choice(self.jump_words)
                        modified = f"{original}——{jump_word}——"

                    result_text = result_text.replace(original, modified, 1)
                    modifications += 1
                    log_debug("熵增扰动", "注入思维中断", 原文=original[:15], 修改后=modified[:15])

        log_info("熵增扰动", "思维中断注入完成", 修改次数=modifications)
        return result_text, modifications


class DialogueImperfector:
    """💬 对话不完美化器 - 让对话包含犹豫、重复、中断等真实特征"""

    def __init__(self):
        # 犹豫词汇
        self.hesitation_words = [
            "嗯...", "呃...", "这个...", "那个...", "怎么说呢...",
            "我是说...", "就是...", "额..."
        ]

        # 重复模式
        self.repetition_patterns = [
            "真的真的", "很很", "非常非常", "特别特别"
        ]

        # 中断词汇
        self.interruption_markers = [
            "——", "...", "——等等", "——不是"
        ]

        log_debug("熵增扰动", "对话不完美化器初始化完成")

    def imperfect_dialogues(self, text: str, intensity: float = 0.3) -> Tuple[str, int]:
        """
        💬 对话不完美化处理

        Args:
            text: 输入文本
            intensity: 不完美化强度 (0.0-1.0)

        Returns:
            Tuple[str, int]: 处理后的文本和修改次数
        """
        log_debug("熵增扰动", "开始对话不完美化", 文本长度=len(text), 强度=intensity)

        modifications = 0
        result_text = text

        # 查找对话内容（支持中英文引号）
        dialogue_pattern = r'[""""]([^""""]*)[""""""]'
        matches = list(re.finditer(dialogue_pattern, result_text))

        for match in matches:
            if random.random() < intensity:
                original_dialogue = match.group(1)
                modified_dialogue = original_dialogue

                # 随机应用不完美化技术
                technique = random.choice(["hesitation", "repetition", "interruption"])

                if technique == "hesitation" and len(original_dialogue) > 5:
                    # 添加犹豫
                    hesitation = random.choice(self.hesitation_words)
                    words = original_dialogue.split()
                    if len(words) > 2:
                        insert_pos = random.randint(1, len(words) - 1)
                        words.insert(insert_pos, hesitation)
                        modified_dialogue = " ".join(words)

                elif technique == "repetition":
                    # 添加重复
                    for pattern in self.repetition_patterns:
                        base_word = pattern.split()[0]
                        if base_word in original_dialogue:
                            modified_dialogue = original_dialogue.replace(base_word, pattern, 1)
                            break

                elif technique == "interruption":
                    # 添加中断
                    if len(original_dialogue) > 10:
                        interrupt_pos = len(original_dialogue) // 2
                        interrupt_marker = random.choice(self.interruption_markers)
                        modified_dialogue = (original_dialogue[:interrupt_pos] +
                                           interrupt_marker +
                                           original_dialogue[interrupt_pos:])

                if modified_dialogue != original_dialogue:
                    # 保持原有的引号类型
                    original_full = match.group(0)
                    quote_start = original_full[0]
                    quote_end = original_full[-1]
                    modified_full = f"{quote_start}{modified_dialogue}{quote_end}"
                    result_text = result_text.replace(original_full, modified_full, 1)
                    modifications += 1
                    log_debug("熵增扰动", "对话不完美化", 技术=technique, 原文=original_dialogue[:10])

        log_info("熵增扰动", "对话不完美化完成", 修改次数=modifications)
        return result_text, modifications


class EntropyInjector:
    """🌀 熵增扰动器主类 - 统一管理所有扰动技术"""

    def __init__(self):
        self.ai_analyzer = AITraceAnalyzer()
        self.perfection_breaker = PerfectionBreaker()
        self.noise_injector = EnvironmentNoiseInjector()
        self.detail_adder = MicroDetailAdder()
        self.thought_interruptor = ThoughtInterruptor()
        self.dialogue_imperfector = DialogueImperfector()

        log_debug("熵增扰动", "熵增扰动器主类初始化完成")

    def process_text(
        self,
        text: str,
        disruption_level: DisruptionLevel = DisruptionLevel.MEDIUM,
        emotion_context: Optional[str] = None,
        custom_intensity: Optional[float] = None
    ) -> DisruptionResult:
        """
        🌀 全方位熵增扰动处理

        Args:
            text: 输入文本
            disruption_level: 扰动级别
            emotion_context: 情感上下文
            custom_intensity: 自定义强度

        Returns:
            DisruptionResult: 扰动处理结果
        """
        log_debug("熵增扰动", "开始全方位熵增扰动处理",
                 文本长度=len(text), 扰动级别=disruption_level.value)

        # 分析原始文本的AI痕迹
        original_analysis = self.ai_analyzer.analyze_ai_traces(text)

        # 根据扰动级别设置强度
        if custom_intensity is not None:
            intensity = custom_intensity
        else:
            intensity_map = {
                DisruptionLevel.LOW: 0.2,
                DisruptionLevel.MEDIUM: 0.4,
                DisruptionLevel.HIGH: 0.6
            }
            intensity = intensity_map[disruption_level]

        # 应用各种扰动技术
        current_text = text
        total_modifications = 0
        applied_techniques = []
        processing_details = {}

        # 1. 破坏完美意象
        current_text, mods = self.perfection_breaker.break_perfect_metaphors(current_text)
        total_modifications += mods
        if mods > 0:
            applied_techniques.append("破坏完美意象")
            processing_details["perfect_metaphor_breaks"] = mods

        # 2. 注入环境噪音
        current_text, mods = self.noise_injector.inject_environmental_noise(current_text, intensity)
        total_modifications += mods
        if mods > 0:
            applied_techniques.append("环境噪音注入")
            processing_details["environmental_noise_injections"] = mods

        # 3. 添加微观细节
        current_text, mods = self.detail_adder.add_micro_details(current_text, emotion_context)
        total_modifications += mods
        if mods > 0:
            applied_techniques.append("微观细节添加")
            processing_details["micro_detail_additions"] = mods

        # 4. 思维中断注入
        current_text, mods = self.thought_interruptor.inject_thought_interruptions(current_text, intensity * 0.5)
        total_modifications += mods
        if mods > 0:
            applied_techniques.append("思维中断注入")
            processing_details["thought_interruptions"] = mods

        # 5. 对话不完美化
        current_text, mods = self.dialogue_imperfector.imperfect_dialogues(current_text, intensity * 0.7)
        total_modifications += mods
        if mods > 0:
            applied_techniques.append("对话不完美化")
            processing_details["dialogue_imperfections"] = mods

        # 分析处理后的AI痕迹
        final_analysis = self.ai_analyzer.analyze_ai_traces(current_text)

        # 计算AI痕迹降低程度
        ai_reduction = max(0, original_analysis["ai_percentage"] - final_analysis["ai_percentage"])

        result = DisruptionResult(
            original_text=text,
            processed_text=current_text,
            modifications_applied=total_modifications,
            disruption_techniques=applied_techniques,
            ai_trace_reduction=ai_reduction,
            processing_details={
                **processing_details,
                "original_ai_percentage": original_analysis["ai_percentage"],
                "final_ai_percentage": final_analysis["ai_percentage"],
                "disruption_level": disruption_level.value,
                "intensity_used": intensity
            }
        )

        log_info("熵增扰动", "全方位熵增扰动处理完成",
                修改次数=total_modifications,
                应用技术数=len(applied_techniques),
                AI痕迹降低=f"{ai_reduction:.1f}%")

        return result


# 便捷函数
def create_entropy_injector() -> EntropyInjector:
    """创建熵增扰动器实例"""
    return EntropyInjector()


def quick_entropy_process(
    text: str,
    level: str = "medium",
    emotion: Optional[str] = None
) -> str:
    """
    快速熵增处理的便捷函数

    Args:
        text: 输入文本
        level: 扰动级别 ("low", "medium", "high")
        emotion: 情感上下文

    Returns:
        str: 处理后的文本
    """
    injector = EntropyInjector()
    disruption_level = DisruptionLevel(level)
    result = injector.process_text(text, disruption_level, emotion)
    return result.processed_text
