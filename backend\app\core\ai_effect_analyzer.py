#!/usr/bin/env python3
"""
🔍 [分析] 去AI化效果分析器
分析文本的AI特征和情感真实性，评估去AI化处理的效果
"""

import re
import jieba
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
from enum import Enum

from app.core.config import log_info, log_debug, log_error


class AnalysisLevel(str, Enum):
    """分析级别枚举"""
    BASIC = "basic"      # 基础分析
    DETAILED = "detailed"  # 详细分析
    COMPREHENSIVE = "comprehensive"  # 全面分析


@dataclass
class AICharacteristics:
    """AI特征分析结果"""
    perfectionism_score: float  # 完美主义评分 (0-1)
    repetition_patterns: List[str]  # 重复模式
    formulaic_expressions: List[str]  # 公式化表达
    unnatural_transitions: List[str]  # 不自然的转换
    overly_structured: bool  # 过度结构化
    ai_confidence: float  # AI特征置信度 (0-1)


@dataclass
class EmotionAuthenticity:
    """情感真实性分析结果"""
    emotion_depth_score: float  # 情感深度评分 (0-1)
    physical_reaction_count: int  # 生理反应数量
    sensory_detail_count: int  # 感官细节数量
    emotional_nuance_score: float  # 情感细腻度评分 (0-1)
    human_like_imperfections: List[str]  # 类人不完美特征
    authenticity_confidence: float  # 真实性置信度 (0-1)


@dataclass
class EffectAnalysisResult:
    """去AI化效果分析结果"""
    original_text: str
    processed_text: Optional[str]
    
    # AI特征分析
    original_ai_characteristics: AICharacteristics
    processed_ai_characteristics: Optional[AICharacteristics]
    ai_reduction_percentage: float  # AI特征降低百分比
    
    # 情感真实性分析
    original_emotion_authenticity: EmotionAuthenticity
    processed_emotion_authenticity: Optional[EmotionAuthenticity]
    emotion_improvement_percentage: float  # 情感改善百分比
    
    # 综合评估
    overall_improvement_score: float  # 总体改善评分 (0-1)
    analysis_level: AnalysisLevel
    analysis_timestamp: str


class AICharacteristicAnalyzer:
    """🤖 AI特征分析器"""
    
    def __init__(self):
        log_debug("分析", "AI特征分析器初始化")
        
        # AI特征关键词和模式
        self.perfectionist_patterns = [
            r'完美的', r'绝对的', r'毫无疑问', r'无可挑剔',
            r'完全正确', r'绝对正确', r'毫不犹豫'
        ]
        
        self.formulaic_expressions = [
            '首先', '其次', '最后', '总之', '综上所述',
            '毫无疑问', '显而易见', '不言而喻', '理所当然'
        ]
        
        self.repetition_indicators = [
            r'(.{3,})\1{2,}',  # 重复短语
            r'(\w+)\s+\1',    # 重复词汇
        ]
        
        self.unnatural_transition_patterns = [
            r'然而，与此同时', r'不仅如此，而且', r'更重要的是',
            r'值得注意的是', r'需要强调的是'
        ]

    def analyze_ai_characteristics(self, text: str) -> AICharacteristics:
        """
        分析文本的AI特征
        
        Args:
            text: 待分析文本
            
        Returns:
            AICharacteristics: AI特征分析结果
        """
        log_debug("分析", "开始AI特征分析", 文本长度=len(text))
        
        # 计算完美主义评分
        perfectionism_score = self._calculate_perfectionism_score(text)
        
        # 检测重复模式
        repetition_patterns = self._detect_repetition_patterns(text)
        
        # 检测公式化表达
        formulaic_expressions = self._detect_formulaic_expressions(text)
        
        # 检测不自然转换
        unnatural_transitions = self._detect_unnatural_transitions(text)
        
        # 判断是否过度结构化
        overly_structured = self._is_overly_structured(text)
        
        # 计算AI置信度
        ai_confidence = self._calculate_ai_confidence(
            perfectionism_score, len(repetition_patterns),
            len(formulaic_expressions), len(unnatural_transitions),
            overly_structured
        )
        
        result = AICharacteristics(
            perfectionism_score=perfectionism_score,
            repetition_patterns=repetition_patterns,
            formulaic_expressions=formulaic_expressions,
            unnatural_transitions=unnatural_transitions,
            overly_structured=overly_structured,
            ai_confidence=ai_confidence
        )
        
        log_info("分析", "AI特征分析完成", 
                AI置信度=f"{ai_confidence:.2f}",
                完美主义评分=f"{perfectionism_score:.2f}")
        
        return result

    def _calculate_perfectionism_score(self, text: str) -> float:
        """计算完美主义评分"""
        total_matches = 0
        for pattern in self.perfectionist_patterns:
            matches = len(re.findall(pattern, text))
            total_matches += matches
        
        # 基于文本长度标准化
        text_length = len(text)
        if text_length == 0:
            return 0.0
        
        score = min(total_matches / (text_length / 100), 1.0)
        return score

    def _detect_repetition_patterns(self, text: str) -> List[str]:
        """检测重复模式"""
        patterns = []
        for pattern in self.repetition_indicators:
            matches = re.findall(pattern, text)
            patterns.extend([match[0] if isinstance(match, tuple) else match for match in matches])
        return patterns[:10]  # 限制返回数量

    def _detect_formulaic_expressions(self, text: str) -> List[str]:
        """检测公式化表达"""
        found_expressions = []
        for expr in self.formulaic_expressions:
            if expr in text:
                found_expressions.append(expr)
        return found_expressions

    def _detect_unnatural_transitions(self, text: str) -> List[str]:
        """检测不自然的转换"""
        transitions = []
        for pattern in self.unnatural_transition_patterns:
            matches = re.findall(pattern, text)
            transitions.extend(matches)
        return transitions

    def _is_overly_structured(self, text: str) -> bool:
        """判断是否过度结构化"""
        # 检查是否有过多的列表标记
        list_markers = len(re.findall(r'[1-9]\.|[一二三四五六七八九十]\.|[①②③④⑤⑥⑦⑧⑨⑩]', text))
        
        # 检查段落结构是否过于规整
        paragraphs = text.split('\n\n')
        if len(paragraphs) > 3:
            avg_length = sum(len(p) for p in paragraphs) / len(paragraphs)
            length_variance = sum((len(p) - avg_length) ** 2 for p in paragraphs) / len(paragraphs)
            # 如果段落长度方差很小，说明过于规整
            if length_variance < avg_length * 0.1:
                return True
        
        return list_markers > len(text) / 200  # 每200字符超过1个列表标记

    def _calculate_ai_confidence(self, perfectionism: float, repetitions: int,
                                formulaic: int, transitions: int, structured: bool) -> float:
        """计算AI特征置信度"""
        score = 0.0
        score += perfectionism * 0.3
        score += min(repetitions / 5, 1.0) * 0.2
        score += min(formulaic / 3, 1.0) * 0.2
        score += min(transitions / 2, 1.0) * 0.2
        score += 0.1 if structured else 0.0
        
        return min(score, 1.0)


class EmotionAuthenticityAnalyzer:
    """💝 情感真实性分析器"""
    
    def __init__(self):
        log_debug("分析", "情感真实性分析器初始化")
        
        # 生理反应关键词
        self.physical_reactions = [
            '心跳', '脸红', '颤抖', '出汗', '紧张', '放松',
            '呼吸', '眨眼', '皱眉', '微笑', '叹气', '咽口水'
        ]
        
        # 感官细节关键词
        self.sensory_details = [
            '看到', '听到', '闻到', '尝到', '触摸', '感受',
            '温暖', '冰冷', '柔软', '粗糙', '明亮', '昏暗',
            '香甜', '苦涩', '清脆', '沉闷'
        ]
        
        # 情感细腻度指标
        self.nuance_indicators = [
            '微微', '轻轻', '淡淡', '隐隐', '悄悄', '慢慢',
            '一丝', '一点', '些许', '略微', '稍微'
        ]
        
        # 类人不完美特征
        self.imperfection_patterns = [
            r'呃[，。]', r'嗯[，。]', r'啊[，。]', r'哦[，。]',
            r'\.{2,}', r'——', r'…', r'！{2,}'
        ]

    def analyze_emotion_authenticity(self, text: str) -> EmotionAuthenticity:
        """
        分析文本的情感真实性
        
        Args:
            text: 待分析文本
            
        Returns:
            EmotionAuthenticity: 情感真实性分析结果
        """
        log_debug("分析", "开始情感真实性分析", 文本长度=len(text))
        
        # 计算情感深度评分
        emotion_depth_score = self._calculate_emotion_depth(text)
        
        # 统计生理反应
        physical_reaction_count = self._count_physical_reactions(text)
        
        # 统计感官细节
        sensory_detail_count = self._count_sensory_details(text)
        
        # 计算情感细腻度评分
        emotional_nuance_score = self._calculate_emotional_nuance(text)
        
        # 检测类人不完美特征
        human_like_imperfections = self._detect_human_imperfections(text)
        
        # 计算真实性置信度
        authenticity_confidence = self._calculate_authenticity_confidence(
            emotion_depth_score, physical_reaction_count,
            sensory_detail_count, emotional_nuance_score,
            len(human_like_imperfections)
        )
        
        result = EmotionAuthenticity(
            emotion_depth_score=emotion_depth_score,
            physical_reaction_count=physical_reaction_count,
            sensory_detail_count=sensory_detail_count,
            emotional_nuance_score=emotional_nuance_score,
            human_like_imperfections=human_like_imperfections,
            authenticity_confidence=authenticity_confidence
        )
        
        log_info("分析", "情感真实性分析完成",
                真实性置信度=f"{authenticity_confidence:.2f}",
                情感深度评分=f"{emotion_depth_score:.2f}")
        
        return result

    def _calculate_emotion_depth(self, text: str) -> float:
        """计算情感深度评分"""
        # 基于情感词汇的丰富度和层次
        words = list(jieba.cut(text))
        emotion_words = [word for word in words if self._is_emotion_word(word)]
        
        if len(words) == 0:
            return 0.0
        
        emotion_ratio = len(emotion_words) / len(words)
        return min(emotion_ratio * 5, 1.0)  # 放大比例并限制在1.0以内

    def _count_physical_reactions(self, text: str) -> int:
        """统计生理反应数量"""
        count = 0
        for reaction in self.physical_reactions:
            count += text.count(reaction)
        return count

    def _count_sensory_details(self, text: str) -> int:
        """统计感官细节数量"""
        count = 0
        for detail in self.sensory_details:
            count += text.count(detail)
        return count

    def _calculate_emotional_nuance(self, text: str) -> float:
        """计算情感细腻度评分"""
        nuance_count = 0
        for indicator in self.nuance_indicators:
            nuance_count += text.count(indicator)
        
        # 基于文本长度标准化
        text_length = len(text)
        if text_length == 0:
            return 0.0
        
        score = min(nuance_count / (text_length / 100), 1.0)
        return score

    def _detect_human_imperfections(self, text: str) -> List[str]:
        """检测类人不完美特征"""
        imperfections = []
        for pattern in self.imperfection_patterns:
            matches = re.findall(pattern, text)
            imperfections.extend(matches)
        return imperfections[:10]  # 限制返回数量

    def _is_emotion_word(self, word: str) -> bool:
        """判断是否为情感词汇"""
        emotion_keywords = [
            '高兴', '快乐', '兴奋', '激动', '开心', '愉快',
            '悲伤', '难过', '伤心', '痛苦', '失望', '沮丧',
            '愤怒', '生气', '恼火', '烦躁', '不满', '愤慨',
            '恐惧', '害怕', '紧张', '担心', '焦虑', '不安',
            '惊讶', '震惊', '意外', '吃惊', '诧异', '惊奇',
            '爱', '喜欢', '钟情', '迷恋', '思念', '想念'
        ]
        return word in emotion_keywords

    def _calculate_authenticity_confidence(self, depth: float, physical: int,
                                         sensory: int, nuance: float, imperfections: int) -> float:
        """计算真实性置信度"""
        score = 0.0
        score += depth * 0.3
        score += min(physical / 5, 1.0) * 0.25
        score += min(sensory / 5, 1.0) * 0.25
        score += nuance * 0.1
        score += min(imperfections / 3, 1.0) * 0.1
        
        return min(score, 1.0)


class AIEffectAnalyzer:
    """🔍 去AI化效果分析器主类"""

    def __init__(self):
        log_debug("分析", "去AI化效果分析器初始化")
        self.ai_analyzer = AICharacteristicAnalyzer()
        self.emotion_analyzer = EmotionAuthenticityAnalyzer()

    def analyze_effect(self, original_text: str, processed_text: Optional[str] = None,
                      analysis_level: AnalysisLevel = AnalysisLevel.DETAILED) -> EffectAnalysisResult:
        """
        分析去AI化处理效果

        Args:
            original_text: 原始文本
            processed_text: 处理后文本（可选）
            analysis_level: 分析级别

        Returns:
            EffectAnalysisResult: 分析结果
        """
        from datetime import datetime

        log_info("分析", "开始去AI化效果分析",
                原始文本长度=len(original_text),
                处理后文本长度=len(processed_text) if processed_text else 0,
                分析级别=analysis_level.value)

        # 分析原始文本
        original_ai_chars = self.ai_analyzer.analyze_ai_characteristics(original_text)
        original_emotion_auth = self.emotion_analyzer.analyze_emotion_authenticity(original_text)

        # 分析处理后文本（如果提供）
        processed_ai_chars = None
        processed_emotion_auth = None
        ai_reduction_percentage = 0.0
        emotion_improvement_percentage = 0.0

        if processed_text:
            processed_ai_chars = self.ai_analyzer.analyze_ai_characteristics(processed_text)
            processed_emotion_auth = self.emotion_analyzer.analyze_emotion_authenticity(processed_text)

            # 计算改善百分比
            ai_reduction_percentage = self._calculate_ai_reduction(
                original_ai_chars.ai_confidence,
                processed_ai_chars.ai_confidence
            )

            emotion_improvement_percentage = self._calculate_emotion_improvement(
                original_emotion_auth.authenticity_confidence,
                processed_emotion_auth.authenticity_confidence
            )

        # 计算总体改善评分
        overall_improvement_score = self._calculate_overall_improvement(
            ai_reduction_percentage, emotion_improvement_percentage
        )

        result = EffectAnalysisResult(
            original_text=original_text,
            processed_text=processed_text,
            original_ai_characteristics=original_ai_chars,
            processed_ai_characteristics=processed_ai_chars,
            ai_reduction_percentage=ai_reduction_percentage,
            original_emotion_authenticity=original_emotion_auth,
            processed_emotion_authenticity=processed_emotion_auth,
            emotion_improvement_percentage=emotion_improvement_percentage,
            overall_improvement_score=overall_improvement_score,
            analysis_level=analysis_level,
            analysis_timestamp=datetime.now().isoformat()
        )

        log_info("分析", "去AI化效果分析完成",
                AI特征降低=f"{ai_reduction_percentage:.1f}%",
                情感改善=f"{emotion_improvement_percentage:.1f}%",
                总体改善评分=f"{overall_improvement_score:.2f}")

        return result

    def _calculate_ai_reduction(self, original_confidence: float, processed_confidence: float) -> float:
        """计算AI特征降低百分比"""
        if original_confidence == 0:
            return 0.0

        reduction = (original_confidence - processed_confidence) / original_confidence
        return max(reduction * 100, 0.0)

    def _calculate_emotion_improvement(self, original_authenticity: float, processed_authenticity: float) -> float:
        """计算情感改善百分比"""
        if original_authenticity >= 1.0:
            return 0.0

        max_possible_improvement = 1.0 - original_authenticity
        actual_improvement = processed_authenticity - original_authenticity

        if max_possible_improvement == 0:
            return 0.0

        improvement_percentage = (actual_improvement / max_possible_improvement) * 100
        return max(improvement_percentage, 0.0)

    def _calculate_overall_improvement(self, ai_reduction: float, emotion_improvement: float) -> float:
        """计算总体改善评分"""
        # 加权平均：AI特征降低占60%，情感改善占40%
        overall_score = (ai_reduction * 0.6 + emotion_improvement * 0.4) / 100
        return min(overall_score, 1.0)

    def generate_analysis_report(self, result: EffectAnalysisResult) -> str:
        """
        生成分析报告

        Args:
            result: 分析结果

        Returns:
            str: 格式化的分析报告
        """
        report = f"""
📊 去AI化效果分析报告
{'='*50}

📈 总体评估
- 总体改善评分: {result.overall_improvement_score:.2f}/1.0
- 分析时间: {result.analysis_timestamp}
- 分析级别: {result.analysis_level.value}

🤖 AI特征分析
原始文本:
- AI特征置信度: {result.original_ai_characteristics.ai_confidence:.2f}
- 完美主义评分: {result.original_ai_characteristics.perfectionism_score:.2f}
- 公式化表达数量: {len(result.original_ai_characteristics.formulaic_expressions)}
- 重复模式数量: {len(result.original_ai_characteristics.repetition_patterns)}
- 过度结构化: {'是' if result.original_ai_characteristics.overly_structured else '否'}
"""

        if result.processed_ai_characteristics:
            report += f"""
处理后文本:
- AI特征置信度: {result.processed_ai_characteristics.ai_confidence:.2f}
- 完美主义评分: {result.processed_ai_characteristics.perfectionism_score:.2f}
- 公式化表达数量: {len(result.processed_ai_characteristics.formulaic_expressions)}
- 重复模式数量: {len(result.processed_ai_characteristics.repetition_patterns)}
- 过度结构化: {'是' if result.processed_ai_characteristics.overly_structured else '否'}

改善效果:
- AI特征降低: {result.ai_reduction_percentage:.1f}%
"""

        report += f"""
💝 情感真实性分析
原始文本:
- 真实性置信度: {result.original_emotion_authenticity.authenticity_confidence:.2f}
- 情感深度评分: {result.original_emotion_authenticity.emotion_depth_score:.2f}
- 生理反应数量: {result.original_emotion_authenticity.physical_reaction_count}
- 感官细节数量: {result.original_emotion_authenticity.sensory_detail_count}
- 情感细腻度: {result.original_emotion_authenticity.emotional_nuance_score:.2f}
- 类人不完美特征: {len(result.original_emotion_authenticity.human_like_imperfections)}
"""

        if result.processed_emotion_authenticity:
            report += f"""
处理后文本:
- 真实性置信度: {result.processed_emotion_authenticity.authenticity_confidence:.2f}
- 情感深度评分: {result.processed_emotion_authenticity.emotion_depth_score:.2f}
- 生理反应数量: {result.processed_emotion_authenticity.physical_reaction_count}
- 感官细节数量: {result.processed_emotion_authenticity.sensory_detail_count}
- 情感细腻度: {result.processed_emotion_authenticity.emotional_nuance_score:.2f}
- 类人不完美特征: {len(result.processed_emotion_authenticity.human_like_imperfections)}

改善效果:
- 情感真实性提升: {result.emotion_improvement_percentage:.1f}%
"""

        return report


def create_ai_effect_analyzer() -> AIEffectAnalyzer:
    """
    🏭 [工厂] 创建去AI化效果分析器实例

    Returns:
        AIEffectAnalyzer: 分析器实例
    """
    return AIEffectAnalyzer()
