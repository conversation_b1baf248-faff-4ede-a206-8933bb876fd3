["tests/core/test_ai_effect_analyzer.py::TestAICharacteristicAnalyzer::test_analyze_high_ai_characteristics", "tests/core/test_ai_effect_analyzer.py::TestAICharacteristicAnalyzer::test_analyze_low_ai_characteristics", "tests/core/test_ai_effect_analyzer.py::TestAICharacteristicAnalyzer::test_detect_repetition_patterns", "tests/core/test_ai_effect_analyzer.py::TestAICharacteristicAnalyzer::test_empty_text_analysis", "tests/core/test_ai_effect_analyzer.py::TestAIEffectAnalyzer::test_analyze_effect_different_levels", "tests/core/test_ai_effect_analyzer.py::TestAIEffectAnalyzer::test_analyze_effect_original_only", "tests/core/test_ai_effect_analyzer.py::TestAIEffectAnalyzer::test_analyze_effect_with_improvement", "tests/core/test_ai_effect_analyzer.py::TestAIEffectAnalyzer::test_calculate_ai_reduction", "tests/core/test_ai_effect_analyzer.py::TestAIEffectAnalyzer::test_calculate_emotion_improvement", "tests/core/test_ai_effect_analyzer.py::TestAIEffectAnalyzer::test_generate_analysis_report", "tests/core/test_ai_effect_analyzer.py::TestConvenienceFunctions::test_create_ai_effect_analyzer", "tests/core/test_ai_effect_analyzer.py::TestEmotionAuthenticityAnalyzer::test_analyze_high_emotion_authenticity", "tests/core/test_ai_effect_analyzer.py::TestEmotionAuthenticityAnalyzer::test_analyze_low_emotion_authenticity", "tests/core/test_ai_effect_analyzer.py::TestEmotionAuthenticityAnalyzer::test_count_physical_reactions", "tests/core/test_ai_effect_analyzer.py::TestEmotionAuthenticityAnalyzer::test_count_sensory_details", "tests/core/test_dna_compiler.py::TestDNACompiler::test_compilation_stats", "tests/core/test_dna_compiler.py::TestDNACompiler::test_compile_basic", "tests/core/test_dna_compiler.py::TestDNACompiler::test_init_dna_compiler", "tests/core/test_dna_compiler.py::TestDNACompilerIntegration::test_full_compilation_workflow", "tests/core/test_dna_compiler.py::TestDehydrator::test_extract_sensory_data", "tests/core/test_dna_compiler.py::TestDehydrator::test_init_dehydrator", "tests/core/test_dna_compiler.py::TestDehydrator::test_remove_lyrical_words", "tests/core/test_dna_compiler.py::TestEmotionalDNA::test_emotional_dna_creation", "tests/core/test_dna_compiler.py::TestEmotionalDNA::test_emotional_dna_to_dict", "tests/core/test_dna_compiler.py::TestEntropyGenerator::test_generate_entropy_items", "tests/core/test_dna_compiler.py::TestEntropyGenerator::test_init_entropy_generator", "tests/core/test_dna_compiler.py::TestGenePairer::test_calculate_intensity_score", "tests/core/test_dna_compiler.py::TestGenePairer::test_calculate_reliability_score", "tests/core/test_dna_compiler.py::TestGenePairer::test_init_gene_pairer", "tests/core/test_dna_compiler.py::TestGenePairer::test_pair_genes", "tests/core/test_dna_compiler.py::TestShellRemover::test_extract_physical_reactions", "tests/core/test_dna_compiler.py::TestShellRemover::test_init_shell_remover", "tests/core/test_dna_compiler.py::TestShellRemover::test_remove_metaphors", "tests/core/test_emotion_miner.py::TestEmotionKeywordDict::test_get_emotion_keywords", "tests/core/test_emotion_miner.py::TestEmotionKeywordDict::test_init_keyword_dict", "tests/core/test_emotion_miner.py::TestEmotionKeywordDict::test_physiological_keywords", "tests/core/test_emotion_miner.py::TestEmotionKeywordDict::test_sensory_keywords", "tests/core/test_emotion_miner.py::TestEmotionMiner::test_empty_statistics", "tests/core/test_emotion_miner.py::TestEmotionMiner::test_get_mining_statistics", "tests/core/test_emotion_miner.py::TestEmotionMiner::test_mine_emotions_from_file", "tests/core/test_emotion_miner.py::TestEmotionMiner::test_mine_emotions_from_text_basic", "tests/core/test_emotion_miner.py::TestEmotionMiner::test_mine_emotions_with_reality_filter", "tests/core/test_emotion_miner.py::TestEmotionMinerIntegration::test_full_mining_workflow", "tests/core/test_emotion_miner.py::TestRealityFilter::test_calculate_reality_score_high", "tests/core/test_emotion_miner.py::TestRealityFilter::test_calculate_reality_score_low", "tests/core/test_emotion_miner.py::TestRealityFilter::test_extract_physiological_indicators", "tests/core/test_emotion_miner.py::TestRealityFilter::test_extract_sensory_details", "tests/core/test_emotion_miner.py::TestTextSegmenter::test_segment_by_paragraphs", "tests/core/test_emotion_miner.py::TestTextSegmenter::test_segment_by_sentences", "tests/core/test_emotion_physical_mapper.py::TestConvenienceFunctions::test_create_emotion_mapper", "tests/core/test_emotion_physical_mapper.py::TestConvenienceFunctions::test_quick_enhance_prompt", "tests/core/test_emotion_physical_mapper.py::TestEmotionPhysicalMapper::test_auto_recognize_and_extract", "tests/core/test_emotion_physical_mapper.py::TestEmotionPhysicalMapper::test_build_enhanced_prompt", "tests/core/test_emotion_physical_mapper.py::TestEmotionPhysicalMapper::test_enhance_prompt_with_auto_detection", "tests/core/test_emotion_physical_mapper.py::TestEmotionPhysicalMapper::test_enhance_prompt_with_specified_emotions", "tests/core/test_emotion_physical_mapper.py::TestEmotionPhysicalMapper::test_extract_emotion_details_exception_handling", "tests/core/test_emotion_physical_mapper.py::TestEmotionPhysicalMapper::test_extract_emotion_details_no_genes_found", "tests/core/test_emotion_physical_mapper.py::TestEmotionPhysicalMapper::test_extract_emotion_details_success", "tests/core/test_emotion_physical_mapper.py::TestEmotionPhysicalMapper::test_generate_enhancement_instructions", "tests/core/test_emotion_physical_mapper.py::TestEmotionRecognizer::test_emotion_deduplication", "tests/core/test_emotion_physical_mapper.py::TestEmotionRecognizer::test_intensity_calculation", "tests/core/test_emotion_physical_mapper.py::TestEmotionRecognizer::test_no_emotions_found", "tests/core/test_emotion_physical_mapper.py::TestEmotionRecognizer::test_recognize_basic_emotions", "tests/core/test_entropy_injector.py::TestAITraceAnalyzer::test_analyze_empty_text", "tests/core/test_entropy_injector.py::TestAITraceAnalyzer::test_analyze_high_ai_traces", "tests/core/test_entropy_injector.py::TestAITraceAnalyzer::test_analyze_natural_text", "tests/core/test_entropy_injector.py::TestConvenienceFunctions::test_create_entropy_injector", "tests/core/test_entropy_injector.py::TestConvenienceFunctions::test_quick_entropy_process", "tests/core/test_entropy_injector.py::TestConvenienceFunctions::test_quick_entropy_process_different_levels", "tests/core/test_entropy_injector.py::TestDialogueImperfector::test_imperfect_dialogues", "tests/core/test_entropy_injector.py::TestDialogueImperfector::test_no_dialogues", "tests/core/test_entropy_injector.py::TestEntropyInjector::test_ai_trace_reduction_calculation", "tests/core/test_entropy_injector.py::TestEntropyInjector::test_process_empty_text", "tests/core/test_entropy_injector.py::TestEntropyInjector::test_process_text_custom_intensity", "tests/core/test_entropy_injector.py::TestEntropyInjector::test_process_text_medium_level", "tests/core/test_entropy_injector.py::TestEntropyInjector::test_process_text_with_emotion_context", "tests/core/test_entropy_injector.py::TestEnvironmentNoiseInjector::test_empty_text_injection", "tests/core/test_entropy_injector.py::TestEnvironmentNoiseInjector::test_inject_environmental_noise", "tests/core/test_entropy_injector.py::TestEnvironmentNoiseInjector::test_low_intensity_injection", "tests/core/test_entropy_injector.py::TestMicroDetailAdder::test_add_micro_details_with_emotion", "tests/core/test_entropy_injector.py::TestMicroDetailAdder::test_add_micro_details_without_emotion", "tests/core/test_entropy_injector.py::TestMicroDetailAdder::test_single_sentence_text", "tests/core/test_entropy_injector.py::TestPerfectionBreaker::test_break_perfect_metaphors", "tests/core/test_entropy_injector.py::TestPerfectionBreaker::test_no_metaphors_to_break", "tests/core/test_entropy_injector.py::TestThoughtInterruptor::test_inject_thought_interruptions", "tests/core/test_entropy_injector.py::TestThoughtInterruptor::test_no_thinking_patterns", "tests/integration/test_deai_pipeline_effect.py::TestDeAIPipelineEffect::test_analysis_report_generation", "tests/integration/test_deai_pipeline_effect.py::TestDeAIPipelineEffect::test_complete_deai_pipeline_effect", "tests/integration/test_deai_pipeline_effect.py::TestDeAIPipelineEffect::test_different_disruption_levels_effect", "tests/integration/test_deai_pipeline_effect.py::TestDeAIPipelineEffect::test_emotion_enhancement_effect", "tests/integration/test_deai_pipeline_effect.py::TestDeAIPipelineEffect::test_entropy_injection_effect", "tests/integration/test_deai_pipeline_effect.py::TestDeAIPipelineEffect::test_pipeline_with_no_improvement_needed", "tests/repositories/test_bible_repo.py::TestChapterRepository::test_create_chapter", "tests/repositories/test_bible_repo.py::TestChapterRepository::test_delete_chapter", "tests/repositories/test_bible_repo.py::TestChapterRepository::test_get_chapter_by_id", "tests/repositories/test_bible_repo.py::TestChapterRepository::test_get_chapters_by_bible_id", "tests/repositories/test_bible_repo.py::TestChapterRepository::test_update_chapter_status", "tests/repositories/test_bible_repo.py::TestStoryBibleRepository::test_create_bible", "tests/repositories/test_bible_repo.py::TestStoryBibleRepository::test_delete_bible", "tests/repositories/test_bible_repo.py::TestStoryBibleRepository::test_get_bible_by_id", "tests/repositories/test_bible_repo.py::TestStoryBibleRepository::test_list_bibles", "tests/repositories/test_bible_repo.py::TestStoryBibleRepository::test_update_bible_status", "tests/repositories/test_chapter_repo.py::TestChapterRepository::test_create_chapter", "tests/repositories/test_chapter_repo.py::TestChapterRepository::test_delete_chapter", "tests/repositories/test_chapter_repo.py::TestChapterRepository::test_get_chapter_by_id", "tests/repositories/test_chapter_repo.py::TestChapterRepository::test_get_chapter_count_by_bible_id", "tests/repositories/test_chapter_repo.py::TestChapterRepository::test_get_chapters_by_bible_id", "tests/repositories/test_chapter_repo.py::TestChapterRepository::test_get_next_chapter_number", "tests/repositories/test_chapter_repo.py::TestChapterRepository::test_update_chapter_status", "tests/repositories/test_emotional_gene_repo.py::TestEmotionalGeneRepository::test_create_gene", "tests/repositories/test_emotional_gene_repo.py::TestEmotionalGeneRepository::test_delete_gene", "tests/repositories/test_emotional_gene_repo.py::TestEmotionalGeneRepository::test_get_gene_by_id", "tests/repositories/test_emotional_gene_repo.py::TestEmotionalGeneRepository::test_get_high_quality_genes", "tests/repositories/test_emotional_gene_repo.py::TestEmotionalGeneRepository::test_get_random_genes", "tests/repositories/test_emotional_gene_repo.py::TestEmotionalGeneRepository::test_get_statistics", "tests/repositories/test_emotional_gene_repo.py::TestEmotionalGeneRepository::test_increment_usage", "tests/repositories/test_emotional_gene_repo.py::TestEmotionalGeneRepository::test_search_genes", "tests/repositories/test_emotional_gene_repo.py::TestEmotionalGeneRepository::test_update_gene", "tests/repositories/test_emotional_gene_repo.py::TestEmotionalGeneSeeder::test_seed_from_dna_list", "tests/repositories/test_story_bible_repo.py::TestStoryBibleRepository::test_create_bible", "tests/repositories/test_story_bible_repo.py::TestStoryBibleRepository::test_delete_bible", "tests/repositories/test_story_bible_repo.py::TestStoryBibleRepository::test_get_bible_by_id", "tests/repositories/test_story_bible_repo.py::TestStoryBibleRepository::test_get_bible_with_chapters", "tests/repositories/test_story_bible_repo.py::TestStoryBibleRepository::test_list_bibles", "tests/repositories/test_story_bible_repo.py::TestStoryBibleRepository::test_update_bible_status", "tests/routers/test_emotion_genes.py::TestEmotionGenesAPI::test_batch_seed_genes", "tests/routers/test_emotion_genes.py::TestEmotionGenesAPI::test_create_emotion_gene", "tests/routers/test_emotion_genes.py::TestEmotionGenesAPI::test_get_emotion_gene", "tests/routers/test_emotion_genes.py::TestEmotionGenesAPI::test_get_high_quality_genes", "tests/routers/test_emotion_genes.py::TestEmotionGenesAPI::test_get_nonexistent_gene", "tests/routers/test_emotion_genes.py::TestEmotionGenesAPI::test_get_random_genes", "tests/routers/test_emotion_genes.py::TestEmotionGenesAPI::test_get_statistics", "tests/routers/test_emotion_genes.py::TestEmotionGenesAPI::test_search_emotion_genes", "tests/routers/test_generation.py::TestGenerationRoutes::test_generate_chapter_bible_not_found", "tests/routers/test_generation.py::TestGenerationRoutes::test_generate_chapter_success", "tests/routers/test_generation.py::TestGenerationRoutes::test_generate_story_bible_invalid_data", "tests/routers/test_generation.py::TestGenerationRoutes::test_generate_story_bible_success", "tests/routers/test_generation.py::TestGenerationRoutes::test_get_task_list_success", "tests/routers/test_generation.py::TestGenerationRoutes::test_get_task_list_with_pagination", "tests/routers/test_generation.py::TestGenerationRoutes::test_get_task_list_with_status_filter", "tests/routers/test_generation.py::TestGenerationRoutes::test_get_task_status_not_found", "tests/routers/test_generation.py::TestGenerationRoutes::test_get_task_status_success", "tests/routers/test_generation.py::TestGenerationRoutes::test_request_data_validation", "tests/routers/test_generation_simple.py::TestPersistenceSimple::test_chapter_without_bible", "tests/routers/test_generation_simple.py::TestPersistenceSimple::test_create_and_get_bible", "tests/routers/test_generation_simple.py::TestPersistenceSimple::test_create_bible_basic", "tests/routers/test_generation_simple.py::TestPersistenceSimple::test_delete_bible", "tests/routers/test_generation_simple.py::TestPersistenceSimple::test_get_nonexistent_bible", "tests/routers/test_generation_simple.py::TestPersistenceSimple::test_list_bibles_empty", "tests/routers/test_persistence_routes.py::TestPersistenceRoutes::test_generate_story_bible", "tests/routers/test_persistence_routes.py::TestPersistenceRoutes::test_get_story_bible", "tests/routers/test_persistence_routes.py::TestPersistenceRoutes::test_list_story_bibles", "tests/routers/test_persistence_simple.py::TestPersistenceSimple::test_chapter_without_bible", "tests/routers/test_persistence_simple.py::TestPersistenceSimple::test_create_and_get_bible", "tests/routers/test_persistence_simple.py::TestPersistenceSimple::test_create_bible_basic", "tests/routers/test_persistence_simple.py::TestPersistenceSimple::test_delete_bible", "tests/routers/test_persistence_simple.py::TestPersistenceSimple::test_get_nonexistent_bible", "tests/routers/test_persistence_simple.py::TestPersistenceSimple::test_list_bibles_empty", "tests/services/test_generation_integration.py::TestGenerationIntegration::test_chapter_generation_emotion_enhancement_error", "tests/services/test_generation_integration.py::TestGenerationIntegration::test_chapter_generation_entropy_injection_error", "tests/services/test_generation_integration.py::TestGenerationIntegration::test_chapter_generation_with_emotion_enhancement", "tests/services/test_generation_integration.py::TestGenerationIntegration::test_chapter_generation_without_enhancement", "tests/services/test_generation_integration.py::TestGenerationIntegration::test_chapter_request_model_validation", "tests/services/test_zhipu_client.py::TestZhipuClient::test_chat_completion_connection_error", "tests/services/test_zhipu_client.py::TestZhipuClient::test_chat_completion_http_error", "tests/services/test_zhipu_client.py::TestZhipuClient::test_chat_completion_stream_success", "tests/services/test_zhipu_client.py::TestZhipuClient::test_chat_completion_success", "tests/services/test_zhipu_client.py::TestZhipuClient::test_chat_completion_timeout", "tests/services/test_zhipu_client.py::TestZhipuClient::test_client_context_manager", "tests/services/test_zhipu_client.py::TestZhipuClient::test_client_initialization", "tests/services/test_zhipu_client.py::TestZhipuClient::test_get_headers", "tests/services/test_zhipu_client.py::TestZhipuClientSingleton::test_close_zhipu_client", "tests/services/test_zhipu_client.py::TestZhipuClientSingleton::test_get_zhipu_client_singleton", "tests/test_database.py::TestAlembicMigrations::test_migration_can_be_applied", "tests/test_database.py::TestDatabaseManager::test_database_manager_initialization", "tests/test_database.py::TestDatabaseManager::test_database_model_operations", "tests/test_database.py::TestDatabaseManager::test_database_session_creation", "tests/test_database.py::TestDatabaseManager::test_database_table_creation", "tests/test_database.py::test_database_initialization_integration", "tests/test_main.py::TestMainApp::test_404_handling", "tests/test_main.py::TestMainApp::test_cors_headers", "tests/test_main.py::TestMainApp::test_health_check_endpoint", "tests/test_main.py::TestMainApp::test_openapi_docs_available", "tests/test_main.py::TestMainApp::test_root_endpoint"]