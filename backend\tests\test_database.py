"""
💾 [数据库] 数据库模块测试
测试数据库连接、迁移和基础操作
"""

import pytest
import tempfile
import os
from pathlib import Path
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy import text

from app.core.database import Base, DatabaseManager, init_database, close_database
from app.core.config import log_info, log_debug
from app.models import StoryBible, Chapter


class TestDatabaseManager:
    """💾 [数据库] 数据库管理器测试类"""
    
    @pytest.fixture
    async def temp_db_manager(self):
        """创建临时数据库管理器用于测试"""
        # 创建临时数据库文件
        temp_dir = tempfile.mkdtemp()
        temp_db_path = Path(temp_dir) / "test.db"
        db_url = f"sqlite+aiosqlite:///{temp_db_path}"
        
        log_debug("数据库", "创建临时测试数据库", 路径=str(temp_db_path))
        
        # 创建数据库管理器
        manager = DatabaseManager()
        manager.initialize(db_url)
        
        # 创建表结构
        async with manager.engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        yield manager
        
        # 清理
        await manager.close()
        try:
            if temp_db_path.exists():
                temp_db_path.unlink()
                log_debug("数据库", "清理临时测试数据库", 路径=str(temp_db_path))
        except PermissionError:
            # Windows上可能出现文件被占用的情况，忽略清理错误
            log_debug("数据库", "临时数据库文件清理跳过（文件被占用）", 路径=str(temp_db_path))
    
    async def test_database_manager_initialization(self, temp_db_manager):
        """💾 [数据库] 测试数据库管理器初始化"""
        manager = temp_db_manager
        
        # 验证管理器已初始化
        assert manager._initialized is True
        assert manager.engine is not None
        assert manager.async_session_maker is not None
        
        log_info("数据库", "数据库管理器初始化测试通过")
    
    async def test_database_session_creation(self, temp_db_manager):
        """💾 [数据库] 测试数据库会话创建"""
        manager = temp_db_manager
        
        # 测试会话创建
        async for session in manager.get_session():
            assert isinstance(session, AsyncSession)
            
            # 测试简单查询
            result = await session.execute(text("SELECT 1 as test_value"))
            row = result.fetchone()
            assert row.test_value == 1
            
            log_info("数据库", "数据库会话创建测试通过", 会话ID=id(session))
            break
    
    async def test_database_table_creation(self, temp_db_manager):
        """💾 [数据库] 测试数据库表创建"""
        manager = temp_db_manager
        
        async for session in manager.get_session():
            # 检查story_bibles表是否存在
            result = await session.execute(
                text("SELECT name FROM sqlite_master WHERE type='table' AND name='story_bibles'")
            )
            table_exists = result.fetchone() is not None
            assert table_exists, "story_bibles表应该存在"
            
            # 检查chapters表是否存在
            result = await session.execute(
                text("SELECT name FROM sqlite_master WHERE type='table' AND name='chapters'")
            )
            table_exists = result.fetchone() is not None
            assert table_exists, "chapters表应该存在"
            
            log_info("数据库", "数据库表创建测试通过")
            break
    
    async def test_database_model_operations(self, temp_db_manager):
        """💾 [数据库] 测试数据库模型基础操作"""
        from app.schemas.generation import StoryGenre, AIProvider, GenerationStatus
        
        manager = temp_db_manager
        
        async for session in manager.get_session():
            # 创建故事圣经记录
            story_bible = StoryBible(
                id="test-bible-001",
                title="测试小说",
                genre=StoryGenre.FANTASY,
                theme="测试主题",
                protagonist="测试主角",
                setting="测试背景",
                plot_outline="测试大纲",
                ai_provider=AIProvider.ZHIPU,
                temperature=0.8,
                max_tokens=3000,
                status=GenerationStatus.PENDING
            )
            
            session.add(story_bible)
            await session.commit()
            
            # 查询验证
            result = await session.execute(
                text("SELECT id, title FROM story_bibles WHERE id = :id"),
                {"id": "test-bible-001"}
            )
            row = result.fetchone()
            assert row is not None
            assert row.id == "test-bible-001"
            assert row.title == "测试小说"
            
            log_info("数据库", "数据库模型操作测试通过", 
                    故事圣经ID=story_bible.id, 
                    标题=story_bible.title)
            break


class TestAlembicMigrations:
    """💾 [数据库] Alembic迁移测试类"""
    
    async def test_migration_can_be_applied(self):
        """💾 [数据库] 测试迁移可以成功应用"""
        # 创建临时数据库
        temp_dir = tempfile.mkdtemp()
        temp_db_path = Path(temp_dir) / "migration_test.db"
        db_url = f"sqlite+aiosqlite:///{temp_db_path}"
        
        try:
            # 创建引擎并应用迁移
            engine = create_async_engine(db_url)
            
            # 创建表结构（模拟迁移应用）
            async with engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
            
            # 验证表是否创建成功
            async_session_maker = async_sessionmaker(bind=engine, class_=AsyncSession)
            async with async_session_maker() as session:
                # 检查表是否存在
                result = await session.execute(
                    text("SELECT name FROM sqlite_master WHERE type='table'")
                )
                tables = [row.name for row in result.fetchall()]
                
                assert "story_bibles" in tables, "story_bibles表应该存在"
                assert "chapters" in tables, "chapters表应该存在"
                
                log_info("数据库", "迁移应用测试通过", 创建的表=tables)
            
            await engine.dispose()
            
        finally:
            # 清理临时文件
            try:
                if temp_db_path.exists():
                    temp_db_path.unlink()
                    log_debug("数据库", "清理迁移测试数据库", 路径=str(temp_db_path))
            except PermissionError:
                # Windows上可能出现文件被占用的情况，忽略清理错误
                log_debug("数据库", "迁移测试数据库文件清理跳过（文件被占用）", 路径=str(temp_db_path))


@pytest.mark.asyncio
async def test_database_initialization_integration():
    """💾 [数据库] 测试数据库初始化集成功能"""
    # 这个测试验证init_database和close_database函数
    # 注意：这个测试使用真实的数据库配置，但不会影响现有数据
    
    try:
        # 测试初始化
        await init_database()
        log_info("数据库", "数据库初始化集成测试 - 初始化完成")
        
        # 测试基本连接
        from app.core.database import db_manager
        async for session in db_manager.get_session():
            result = await session.execute(text("SELECT 1 as test"))
            assert result.fetchone().test == 1
            log_info("数据库", "数据库初始化集成测试 - 连接测试通过")
            break
        
    finally:
        # 清理
        await close_database()
        log_info("数据库", "数据库初始化集成测试 - 清理完成")
