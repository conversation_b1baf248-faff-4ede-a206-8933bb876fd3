#!/usr/bin/env python3
"""
🧪 [测试] 情感增强流水线集成测试
测试生成服务与情感增强流水线的集成功能
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from app.services.generation_service import generate_chapter_content
from app.schemas.generation import ChapterGenerationRequest, AIProvider
from app.repositories.emotional_gene_repo import EmotionalGeneRepository
from app.core.config import log_debug


class TestGenerationIntegration:
    """🧪 生成服务集成测试类"""

    def setup_method(self):
        """🔧 [调试] 测试初始化"""
        log_debug("测试", "生成服务集成测试初始化")

    @pytest.mark.asyncio
    async def test_chapter_generation_with_emotion_enhancement(self):
        """测试启用情感增强的章节生成"""
        # 创建测试请求
        request = ChapterGenerationRequest(
            story_bible_id="test-bible-123",
            chapter_number=1,
            chapter_title="第一章：相遇",
            chapter_outline="男女主角在咖啡厅初次相遇，产生微妙的情感波动",
            enable_emotion_enhancement=True,
            specified_emotions=["紧张", "期待"],
            emotion_intensity=0.8,
            auto_detect_emotions=True,
            enable_entropy_injection=True,
            disruption_level="medium"
        )

        story_bible = "这是一个现代都市言情小说，讲述了..."

        # 模拟情感基因仓库
        mock_gene_repo = AsyncMock(spec=EmotionalGeneRepository)
        
        # 模拟智谱AI客户端
        mock_response = MagicMock()
        mock_response.choices = [{"message": {"content": "生成的章节内容，包含对话和情感描写。"}}]
        
        with patch('app.services.generation_service.get_zhipu_client') as mock_client:
            mock_client.return_value.chat_completion = AsyncMock(return_value=mock_response)
            
            # 模拟情感映射器
            with patch('app.services.generation_service.create_emotion_mapper') as mock_mapper_creator:
                mock_mapper = AsyncMock()
                mock_enhanced_result = MagicMock()
                mock_enhanced_result.enhanced_prompt = "增强后的提示词"
                mock_enhanced_result.enhancement_instructions = ["指令1", "指令2"]
                mock_enhanced_result.emotion_mappings = [MagicMock(emotion_tag="紧张")]
                
                mock_mapper.enhance_prompt_with_emotions = AsyncMock(return_value=mock_enhanced_result)
                mock_mapper_creator.return_value = mock_mapper
                
                # 模拟熵增扰动器
                with patch('app.services.generation_service.create_entropy_injector') as mock_injector_creator:
                    mock_injector = MagicMock()
                    mock_disruption_result = MagicMock()
                    mock_disruption_result.processed_text = "经过熵增扰动处理的最终内容"
                    mock_disruption_result.modifications_applied = 3
                    mock_disruption_result.disruption_techniques = ["technique1", "technique2"]
                    mock_disruption_result.ai_trace_reduction = 15.5
                    
                    mock_injector.process_text = MagicMock(return_value=mock_disruption_result)
                    mock_injector_creator.return_value = mock_injector
                    
                    # 执行测试
                    result = await generate_chapter_content(request, story_bible, mock_gene_repo)
                    
                    # 验证结果
                    assert result == "经过熵增扰动处理的最终内容"
                    
                    # 验证情感映射器被调用
                    mock_mapper_creator.assert_called_once_with(mock_gene_repo)
                    mock_mapper.enhance_prompt_with_emotions.assert_called_once()
                    
                    # 验证熵增扰动器被调用
                    mock_injector_creator.assert_called_once()
                    mock_injector.process_text.assert_called_once()

    @pytest.mark.asyncio
    async def test_chapter_generation_without_enhancement(self):
        """测试禁用情感增强的章节生成"""
        # 创建测试请求（禁用增强功能）
        request = ChapterGenerationRequest(
            story_bible_id="test-bible-123",
            chapter_number=1,
            chapter_title="第一章：相遇",
            chapter_outline="男女主角在咖啡厅初次相遇",
            enable_emotion_enhancement=False,
            enable_entropy_injection=False
        )

        story_bible = "这是一个现代都市言情小说..."

        # 模拟智谱AI客户端
        mock_response = MagicMock()
        mock_response.choices = [{"message": {"content": "原始生成的章节内容"}}]
        
        with patch('app.services.generation_service.get_zhipu_client') as mock_client:
            mock_client.return_value.chat_completion = AsyncMock(return_value=mock_response)
            
            # 执行测试
            result = await generate_chapter_content(request, story_bible, None)
            
            # 验证结果（应该是原始内容，没有经过增强处理）
            assert result == "原始生成的章节内容"

    @pytest.mark.asyncio
    async def test_chapter_generation_emotion_enhancement_error(self):
        """测试情感增强处理出错时的降级处理"""
        request = ChapterGenerationRequest(
            story_bible_id="test-bible-123",
            chapter_number=1,
            chapter_title="第一章：相遇",
            chapter_outline="男女主角在咖啡厅初次相遇",
            enable_emotion_enhancement=True,
            enable_entropy_injection=False
        )

        story_bible = "这是一个现代都市言情小说..."
        mock_gene_repo = AsyncMock(spec=EmotionalGeneRepository)

        # 模拟智谱AI客户端
        mock_response = MagicMock()
        mock_response.choices = [{"message": {"content": "原始生成的章节内容"}}]
        
        with patch('app.services.generation_service.get_zhipu_client') as mock_client:
            mock_client.return_value.chat_completion = AsyncMock(return_value=mock_response)
            
            # 模拟情感映射器抛出异常
            with patch('app.services.generation_service.create_emotion_mapper') as mock_mapper_creator:
                mock_mapper_creator.side_effect = Exception("情感映射器错误")
                
                # 执行测试
                result = await generate_chapter_content(request, story_bible, mock_gene_repo)
                
                # 验证结果（应该降级到原始内容）
                assert result == "原始生成的章节内容"

    @pytest.mark.asyncio
    async def test_chapter_generation_entropy_injection_error(self):
        """测试熵增扰动处理出错时的降级处理"""
        request = ChapterGenerationRequest(
            story_bible_id="test-bible-123",
            chapter_number=1,
            chapter_title="第一章：相遇",
            chapter_outline="男女主角在咖啡厅初次相遇",
            enable_emotion_enhancement=False,
            enable_entropy_injection=True
        )

        story_bible = "这是一个现代都市言情小说..."

        # 模拟智谱AI客户端
        mock_response = MagicMock()
        mock_response.choices = [{"message": {"content": "原始生成的章节内容"}}]
        
        with patch('app.services.generation_service.get_zhipu_client') as mock_client:
            mock_client.return_value.chat_completion = AsyncMock(return_value=mock_response)
            
            # 模拟熵增扰动器抛出异常
            with patch('app.services.generation_service.create_entropy_injector') as mock_injector_creator:
                mock_injector = MagicMock()
                mock_injector.process_text.side_effect = Exception("熵增扰动器错误")
                mock_injector_creator.return_value = mock_injector
                
                # 执行测试
                result = await generate_chapter_content(request, story_bible, None)
                
                # 验证结果（应该降级到原始内容）
                assert result == "原始生成的章节内容"

    def test_chapter_request_model_validation(self):
        """测试章节请求模型的参数验证"""
        # 测试有效请求
        valid_request = ChapterGenerationRequest(
            story_bible_id="test-bible-123",
            chapter_number=1,
            chapter_title="第一章",
            chapter_outline="这是一个详细的章节大纲内容描述",
            specified_emotions=["紧张", "期待", "兴奋"],
            emotion_intensity=0.8,
            disruption_level="high",
            custom_disruption_intensity=0.6
        )
        
        assert valid_request.enable_emotion_enhancement is True  # 默认值
        assert valid_request.auto_detect_emotions is True  # 默认值
        assert valid_request.enable_entropy_injection is True  # 默认值
        assert valid_request.disruption_level == "high"
        assert valid_request.custom_disruption_intensity == 0.6

        # 测试边界值
        boundary_request = ChapterGenerationRequest(
            story_bible_id="test-bible-123",
            chapter_number=1,
            chapter_title="第一章",
            chapter_outline="这是一个测试边界值的章节大纲内容",
            emotion_intensity=1.0,  # 最大值
            custom_disruption_intensity=0.0  # 最小值
        )
        
        assert boundary_request.emotion_intensity == 1.0
        assert boundary_request.custom_disruption_intensity == 0.0
