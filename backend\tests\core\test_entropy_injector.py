"""
🧪 [测试] 熵增扰动器测试模块

测试AI痕迹分析、完美意象破坏、环境噪音注入等去AI化功能

作者: 文心小说后端服务系统
创建时间: 2025-08-02
"""

import pytest
from unittest.mock import patch
from typing import List

from app.core.entropy_injector import (
    EntropyInjector, AITraceAnalyzer, PerfectionBreaker,
    EnvironmentNoiseInjector, MicroDetailAdder, ThoughtInterruptor,
    DialogueImperfector, DisruptionLevel, DisruptionResult,
    create_entropy_injector, quick_entropy_process
)


class TestAITraceAnalyzer:
    """AI痕迹分析器测试类"""
    
    def setup_method(self):
        """测试前准备"""
        self.analyzer = AITraceAnalyzer()
    
    def test_analyze_high_ai_traces(self):
        """测试高AI痕迹文本分析"""
        # 包含多种AI特征的文本
        text = "他不仅愤怒而且悲伤，既想要报复又想要原谅，一方面感到愤怒，另一方面又觉得无奈。"
        result = self.analyzer.analyze_ai_traces(text)
        
        assert result["ai_percentage"] > 50  # 应该检测到高AI痕迹
        assert result["total_matches"] > 0
        assert "完美句式" in result["pattern_matches"]
        assert result["assessment"] in ["高AI痕迹", "极高AI痕迹"]
    
    def test_analyze_natural_text(self):
        """测试自然文本分析"""
        text = "他有点生气，但也说不清为什么。可能是因为今天的事情吧。"
        result = self.analyzer.analyze_ai_traces(text)
        
        assert result["ai_percentage"] < 30  # 应该是较低的AI痕迹
        assert result["assessment"] in ["自然文本", "低AI痕迹"]
    
    def test_analyze_empty_text(self):
        """测试空文本分析"""
        result = self.analyzer.analyze_ai_traces("")
        
        assert result["ai_percentage"] == 0.0
        assert result["total_matches"] == 0
        assert result["text_length"] == 0


class TestPerfectionBreaker:
    """完美意象破坏器测试类"""
    
    def setup_method(self):
        """测试前准备"""
        self.breaker = PerfectionBreaker()
    
    def test_break_perfect_metaphors(self):
        """测试破坏完美比喻"""
        text = "她的眼睛像星星一样闪亮，声音如同天籁般动听。"
        result_text, modifications = self.breaker.break_perfect_metaphors(text)
        
        # 应该有修改
        assert modifications > 0
        assert result_text != text
        # 应该包含不确定词汇
        uncertainty_found = any(word in result_text for word in self.breaker.uncertainty_words)
        assert uncertainty_found
    
    def test_no_metaphors_to_break(self):
        """测试没有比喻的文本"""
        text = "他走进房间，坐在椅子上。"
        result_text, modifications = self.breaker.break_perfect_metaphors(text)
        
        assert modifications == 0
        assert result_text == text


class TestEnvironmentNoiseInjector:
    """环境噪音注入器测试类"""
    
    def setup_method(self):
        """测试前准备"""
        self.injector = EnvironmentNoiseInjector()
    
    def test_inject_environmental_noise(self):
        """测试环境噪音注入"""
        text = "他愤怒地看着她。她低下了头。"
        result_text, modifications = self.injector.inject_environmental_noise(text, intensity=0.8)
        
        # 高强度下应该有修改
        assert len(result_text) > len(text)  # 文本应该变长
        # 应该包含噪音库中的内容
        noise_found = False
        for noise_list in self.injector.noise_library.values():
            if any(noise in result_text for noise in noise_list):
                noise_found = True
                break
        assert noise_found
    
    def test_low_intensity_injection(self):
        """测试低强度注入"""
        text = "他愤怒地看着她。她低下了头。"
        result_text, modifications = self.injector.inject_environmental_noise(text, intensity=0.1)
        
        # 低强度下修改应该较少
        assert modifications <= 1
    
    def test_empty_text_injection(self):
        """测试空文本注入"""
        result_text, modifications = self.injector.inject_environmental_noise("", intensity=0.5)
        
        assert modifications == 0
        assert result_text == ""


class TestMicroDetailAdder:
    """微观细节添加器测试类"""
    
    def setup_method(self):
        """测试前准备"""
        self.adder = MicroDetailAdder()
    
    def test_add_micro_details_with_emotion(self):
        """测试带情感上下文的微观细节添加"""
        text = "他感到非常愤怒。她开始哭泣。"
        result_text, modifications = self.adder.add_micro_details(text, emotion_context="愤怒")
        
        assert modifications > 0
        assert len(result_text) > len(text)
        # 应该包含愤怒相关的微反应
        anger_reactions = self.adder.micro_reactions["愤怒"]
        reaction_found = any(reaction in result_text for reaction in anger_reactions)
        assert reaction_found
    
    def test_add_micro_details_without_emotion(self):
        """测试无情感上下文的微观细节添加"""
        text = "他们在房间里交谈。气氛有些紧张。"
        result_text, modifications = self.adder.add_micro_details(text)
        
        # 应该随机选择情感类型并添加细节
        assert len(result_text) >= len(text)
    
    def test_single_sentence_text(self):
        """测试单句文本"""
        text = "他很生气"
        result_text, modifications = self.adder.add_micro_details(text, emotion_context="愤怒")
        
        # 单句文本可能不会修改
        assert result_text is not None


class TestThoughtInterruptor:
    """思维中断注入器测试类"""
    
    def setup_method(self):
        """测试前准备"""
        self.interruptor = ThoughtInterruptor()
    
    def test_inject_thought_interruptions(self):
        """测试思维中断注入"""
        text = "他想这件事情很奇怪。他觉得应该调查一下。他认为真相就在眼前。"
        result_text, modifications = self.interruptor.inject_thought_interruptions(text, intensity=0.8)
        
        # 高强度下应该有修改
        assert modifications > 0
        # 应该包含中断词汇
        interrupt_found = any(word in result_text for word in self.interruptor.interruption_words)
        jump_found = any(word in result_text for word in self.interruptor.jump_words)
        assert interrupt_found or jump_found
    
    def test_no_thinking_patterns(self):
        """测试没有思考模式的文本"""
        text = "他走进房间。桌子上有一本书。"
        result_text, modifications = self.interruptor.inject_thought_interruptions(text, intensity=0.8)
        
        assert modifications == 0
        assert result_text == text


class TestDialogueImperfector:
    """对话不完美化器测试类"""
    
    def setup_method(self):
        """测试前准备"""
        self.imperfector = DialogueImperfector()
    
    def test_imperfect_dialogues(self):
        """测试对话不完美化"""
        import random
        random.seed(42)  # 固定随机种子

        text = '他说："我觉得这件事很重要。"她回答："我完全同意你的观点。"'
        result_text, modifications = self.imperfector.imperfect_dialogues(text, intensity=1.0)  # 100%强度

        # 100%强度下应该有修改
        assert modifications >= 0  # 至少不会出错
        # 检查是否找到了对话
        import re
        dialogue_pattern = r'[""""]([^""""]*)[""""""]'
        matches = list(re.finditer(dialogue_pattern, text))
        assert len(matches) == 2  # 应该找到2个对话
    
    def test_no_dialogues(self):
        """测试没有对话的文本"""
        text = "他走进房间，看到桌子上有一本书。"
        result_text, modifications = self.imperfector.imperfect_dialogues(text, intensity=0.8)
        
        assert modifications == 0
        assert result_text == text


class TestEntropyInjector:
    """熵增扰动器主类测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.injector = EntropyInjector()
    
    def test_process_text_medium_level(self):
        """测试中等级别的文本处理"""
        text = "他不仅愤怒而且悲伤，像火山一样爆发。他想这一切都太完美了。"
        result = self.injector.process_text(text, DisruptionLevel.MEDIUM)
        
        assert isinstance(result, DisruptionResult)
        assert result.original_text == text
        assert result.processed_text != text
        assert result.modifications_applied > 0
        assert len(result.disruption_techniques) > 0
        assert result.ai_trace_reduction >= 0
    
    def test_process_text_with_emotion_context(self):
        """测试带情感上下文的处理"""
        text = "他感到愤怒，心中充满了怒火。"
        result = self.injector.process_text(text, DisruptionLevel.HIGH, emotion_context="愤怒")
        
        assert result.modifications_applied > 0
        assert "微观细节添加" in result.disruption_techniques
    
    def test_process_text_custom_intensity(self):
        """测试自定义强度处理"""
        text = "这是一个完美的故事，不仅精彩而且动人。"
        result = self.injector.process_text(text, custom_intensity=0.9)
        
        assert result.processing_details["intensity_used"] == 0.9
        assert result.modifications_applied > 0
    
    def test_process_empty_text(self):
        """测试空文本处理"""
        result = self.injector.process_text("")
        
        assert result.original_text == ""
        assert result.processed_text == ""
        assert result.modifications_applied == 0
    
    def test_ai_trace_reduction_calculation(self):
        """测试AI痕迹降低计算"""
        # 高AI痕迹文本
        text = "他不仅愤怒而且悲伤，既想要报复又想要原谅，一方面感到愤怒，另一方面又觉得无奈。"
        result = self.injector.process_text(text, DisruptionLevel.HIGH)
        
        # 应该有AI痕迹降低
        assert result.ai_trace_reduction >= 0
        assert "original_ai_percentage" in result.processing_details
        assert "final_ai_percentage" in result.processing_details


class TestConvenienceFunctions:
    """便捷函数测试类"""
    
    def test_create_entropy_injector(self):
        """测试创建熵增扰动器"""
        injector = create_entropy_injector()
        
        assert isinstance(injector, EntropyInjector)
        assert hasattr(injector, 'ai_analyzer')
        assert hasattr(injector, 'perfection_breaker')
    
    def test_quick_entropy_process(self):
        """测试快速熵增处理"""
        text = "这是一个完美的故事。"
        result = quick_entropy_process(text, level="medium", emotion="愤怒")
        
        assert isinstance(result, str)
        assert len(result) >= len(text)  # 处理后文本长度应该不小于原文
    
    def test_quick_entropy_process_different_levels(self):
        """测试不同级别的快速处理"""
        text = "他不仅愤怒而且悲伤。"
        
        # 测试不同级别
        for level in ["low", "medium", "high"]:
            result = quick_entropy_process(text, level=level)
            assert isinstance(result, str)
            assert len(result) > 0
