"""
💾 [数据库] 数据库配置和连接管理
提供SQLAlchemy异步数据库连接、会话管理和依赖注入功能
"""

from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from fastapi import Depends
import os
from pathlib import Path

from app.core.config import settings, log_info, log_error, log_debug
from app.core.base import Base


class DatabaseManager:
    """
    💾 [数据库] 数据库管理器
    负责数据库引擎创建、会话管理和连接池配置
    """
    
    def __init__(self):
        self.engine = None
        self.async_session_maker = None
        self._initialized = False
    
    def initialize(self, database_url: str = None) -> None:
        """
        💾 [数据库] 初始化数据库连接
        
        Args:
            database_url: 数据库连接URL，如果为None则使用配置中的URL
        """
        if self._initialized:
            log_debug("数据库", "数据库管理器已经初始化，跳过重复初始化")
            return
        
        # 使用传入的URL或配置中的URL
        db_url = database_url or settings.database_url
        
        log_info("数据库", "开始初始化数据库连接", 数据库URL=db_url)
        
        try:
            # 确保数据库目录存在
            if db_url.startswith("sqlite"):
                # 从URL中提取文件路径
                db_path = db_url.replace("sqlite+aiosqlite:///", "")
                db_dir = Path(db_path).parent
                db_dir.mkdir(parents=True, exist_ok=True)
                log_debug("数据库", "确保SQLite数据库目录存在", 目录=str(db_dir))
            
            # 创建异步引擎
            self.engine = create_async_engine(
                db_url,
                echo=settings.database_echo,  # 是否打印SQL语句
                pool_pre_ping=True,  # 连接前检查连接是否有效
                pool_recycle=3600,   # 连接回收时间（秒）
            )
            
            # 创建异步会话工厂
            self.async_session_maker = async_sessionmaker(
                bind=self.engine,
                class_=AsyncSession,
                expire_on_commit=False,  # 提交后不过期对象
                autoflush=True,          # 自动刷新
                autocommit=False,        # 不自动提交
            )
            
            self._initialized = True
            log_info("数据库", "数据库连接初始化完成", 
                    引擎类型=type(self.engine).__name__,
                    会话工厂=type(self.async_session_maker).__name__)
            
        except Exception as e:
            log_error("数据库", "数据库连接初始化失败", error=e)
            raise
    
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """
        💾 [数据库] 获取数据库会话
        
        这是一个异步生成器，用于FastAPI的依赖注入系统
        会话会在使用完毕后自动关闭
        
        Yields:
            AsyncSession: 异步数据库会话
        """
        if not self._initialized:
            raise RuntimeError("数据库管理器尚未初始化，请先调用initialize()方法")
        
        async with self.async_session_maker() as session:
            try:
                log_debug("数据库", "创建新的数据库会话", 会话ID=id(session))
                yield session
            except Exception as e:
                log_error("数据库", "数据库会话发生错误", error=e, 会话ID=id(session))
                await session.rollback()
                raise
            finally:
                log_debug("数据库", "关闭数据库会话", 会话ID=id(session))
                await session.close()
    
    async def close(self) -> None:
        """
        💾 [数据库] 关闭数据库连接
        """
        if self.engine:
            log_info("数据库", "正在关闭数据库连接")
            await self.engine.dispose()
            self.engine = None
            self.async_session_maker = None
            self._initialized = False
            log_info("数据库", "数据库连接已关闭")


# 全局数据库管理器实例
db_manager = DatabaseManager()


async def get_database_session() -> AsyncGenerator[AsyncSession, None]:
    """
    💾 [数据库] FastAPI依赖注入函数
    
    用于在路由中注入数据库会话
    
    使用方式:
    ```python
    @router.get("/example")
    async def example_route(db: AsyncSession = Depends(get_database_session)):
        # 使用db进行数据库操作
        pass
    ```
    
    Yields:
        AsyncSession: 异步数据库会话
    """
    async for session in db_manager.get_session():
        yield session


async def init_database() -> None:
    """
    💾 [数据库] 初始化数据库
    
    在应用启动时调用，初始化数据库连接和创建表结构
    """
    log_info("数据库", "开始初始化数据库系统")
    
    try:
        # 初始化数据库管理器
        db_manager.initialize()
        
        # 创建所有表（如果不存在）
        async with db_manager.engine.begin() as conn:
            log_info("数据库", "开始创建数据库表结构")
            await conn.run_sync(Base.metadata.create_all)
            log_info("数据库", "数据库表结构创建完成")
        
        log_info("数据库", "数据库系统初始化完成")
        
    except Exception as e:
        log_error("数据库", "数据库系统初始化失败", error=e)
        raise


async def close_database() -> None:
    """
    💾 [数据库] 关闭数据库连接

    在应用关闭时调用
    """
    await db_manager.close()


# 仓库依赖注入函数
async def get_bible_repository(session: AsyncSession = Depends(get_database_session)):
    """
    💾 [数据库] 获取故事圣经仓库

    用于FastAPI依赖注入的故事圣经仓库实例

    Args:
        session: 数据库会话

    Returns:
        StoryBibleRepository: 故事圣经仓库实例
    """
    from app.repositories.bible_repo import StoryBibleRepository
    log_debug("数据库", "创建故事圣经仓库实例")
    return StoryBibleRepository(session)


async def get_chapter_repository(session: AsyncSession = Depends(get_database_session)):
    """
    💾 [数据库] 获取章节仓库

    用于FastAPI依赖注入的章节仓库实例

    Args:
        session: 数据库会话

    Returns:
        ChapterRepository: 章节仓库实例
    """
    from app.repositories.chapter_repo import ChapterRepository
    log_debug("数据库", "创建章节仓库实例")
    return ChapterRepository(session)
