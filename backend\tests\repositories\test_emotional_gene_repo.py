"""
🧬 情感基因仓库测试模块

测试情感基因仓库的各种功能：
1. 基础CRUD操作
2. 高级检索功能
3. 统计分析功能
4. 批量操作测试

作者: 文心小说后端服务系统
创建时间: 2025-08-02
"""

import pytest
from datetime import datetime, timedelta
from typing import Dict, List, Any

from app.repositories.emotional_gene_repo import EmotionalGeneRepository, EmotionalGeneSeeder
from app.models.emotional_gene import EmotionalGene, EmotionalGeneStats
from app.core.config import log_debug, log_info, log_error


class TestEmotionalGeneRepository:
    """🧬 [情感基因仓库测试] 测试情感基因仓库功能"""
    
    @pytest.fixture
    async def sample_gene_data(self) -> Dict[str, Any]:
        """创建测试用的基因数据"""
        return {
            "emotion_tag": "恐惧",
            "source_text": "他感到强烈的恐惧，心跳剧烈，呼吸急促。",
            "physiological_reactions": ["心跳加速", "呼吸急促", "手掌出汗"],
            "sensory_triggers": ["看到阴影", "听到脚步声"],
            "entropy_items": ["周围传来嘈杂的声音"],
            "intensity_score": 0.8,
            "reliability_score": 0.7,
            "purification_level": "basic",
            "category": "负面情感",
            "subcategory": "恐惧类",
            "source_type": "文学作品",
            "source_author": "测试作者",
            "source_title": "测试小说"
        }
    
    @pytest.fixture
    async def test_session(self):
        """创建测试数据库会话"""
        import tempfile
        from pathlib import Path
        from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
        from app.core.database import Base

        # 创建临时数据库
        temp_dir = tempfile.mkdtemp()
        temp_db_path = Path(temp_dir) / "test_emotional_gene_repo.db"
        db_url = f"sqlite+aiosqlite:///{temp_db_path}"

        log_debug("数据库", "创建情感基因仓库测试数据库", 路径=str(temp_db_path))

        # 创建引擎和会话
        engine = create_async_engine(db_url)
        async_session_maker = async_sessionmaker(bind=engine, class_=AsyncSession)

        # 创建表结构
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)

        # 提供会话
        async with async_session_maker() as session:
            yield session

        # 清理
        await engine.dispose()
        try:
            if temp_db_path.exists():
                temp_db_path.unlink()
        except PermissionError:
            pass

    @pytest.fixture
    async def repository(self, test_session):
        """创建情感基因仓库实例"""
        return EmotionalGeneRepository(test_session)
    
    async def test_create_gene(self, repository: EmotionalGeneRepository, sample_gene_data: Dict[str, Any]):
        """测试创建情感基因"""
        log_debug("测试", "开始测试创建情感基因")
        
        gene = await repository.create_gene(sample_gene_data)
        
        # 验证创建结果
        assert gene.id is not None
        assert gene.emotion_tag == sample_gene_data["emotion_tag"]
        assert gene.source_text == sample_gene_data["source_text"]
        assert gene.physiological_reactions == sample_gene_data["physiological_reactions"]
        assert gene.sensory_triggers == sample_gene_data["sensory_triggers"]
        assert gene.entropy_items == sample_gene_data["entropy_items"]
        assert gene.intensity_score == sample_gene_data["intensity_score"]
        assert gene.reliability_score == sample_gene_data["reliability_score"]
        assert gene.quality_score > 0.0  # 质量评分应该被自动计算
        assert gene.created_at is not None
        assert gene.updated_at is not None
        
        log_info("测试", "创建情感基因测试通过", 基因ID=gene.id)
    
    async def test_get_gene_by_id(self, repository: EmotionalGeneRepository, sample_gene_data: Dict[str, Any]):
        """测试根据ID获取情感基因"""
        log_debug("测试", "开始测试根据ID获取情感基因")
        
        # 先创建一个基因
        created_gene = await repository.create_gene(sample_gene_data)
        
        # 根据ID获取
        retrieved_gene = await repository.get_gene_by_id(created_gene.id)
        
        # 验证结果
        assert retrieved_gene is not None
        assert retrieved_gene.id == created_gene.id
        assert retrieved_gene.emotion_tag == created_gene.emotion_tag
        assert retrieved_gene.source_text == created_gene.source_text
        
        # 测试不存在的ID
        non_existent_gene = await repository.get_gene_by_id(99999)
        assert non_existent_gene is None
        
        log_info("测试", "根据ID获取情感基因测试通过")
    
    async def test_search_genes(self, repository: EmotionalGeneRepository, sample_gene_data: Dict[str, Any]):
        """测试搜索情感基因"""
        log_debug("测试", "开始测试搜索情感基因")
        
        # 创建多个测试基因
        gene1_data = sample_gene_data.copy()
        gene1_data["emotion_tag"] = "恐惧"
        gene1_data["category"] = "负面情感"
        gene1_data["intensity_score"] = 0.8
        
        gene2_data = sample_gene_data.copy()
        gene2_data["emotion_tag"] = "愤怒"
        gene2_data["category"] = "负面情感"
        gene2_data["intensity_score"] = 0.9
        
        gene3_data = sample_gene_data.copy()
        gene3_data["emotion_tag"] = "喜悦"
        gene3_data["category"] = "正面情感"
        gene3_data["intensity_score"] = 0.7
        
        await repository.create_gene(gene1_data)
        await repository.create_gene(gene2_data)
        await repository.create_gene(gene3_data)
        
        # 测试按情感标签搜索
        results, total = await repository.search_genes(emotion_tag="恐惧")
        assert total >= 1
        assert any(gene.emotion_tag == "恐惧" for gene in results)
        
        # 测试按类别搜索
        results, total = await repository.search_genes(category="负面情感")
        assert total >= 2
        assert all(gene.category == "负面情感" for gene in results)
        
        # 测试按强度搜索
        results, total = await repository.search_genes(min_intensity=0.85)
        assert total >= 1
        assert all(gene.intensity_score >= 0.85 for gene in results)
        
        # 测试组合搜索
        results, total = await repository.search_genes(
            category="负面情感",
            min_intensity=0.75
        )
        assert total >= 2
        
        log_info("测试", "搜索情感基因测试通过")
    
    async def test_get_random_genes(self, repository: EmotionalGeneRepository, sample_gene_data: Dict[str, Any]):
        """测试获取随机情感基因"""
        log_debug("测试", "开始测试获取随机情感基因")
        
        # 创建多个基因
        for i in range(5):
            gene_data = sample_gene_data.copy()
            gene_data["emotion_tag"] = f"情感{i}"
            await repository.create_gene(gene_data)
        
        # 获取随机基因
        random_genes = await repository.get_random_genes(count=3)
        
        # 验证结果
        assert len(random_genes) <= 3
        assert all(isinstance(gene, EmotionalGene) for gene in random_genes)
        
        # 测试按情感标签筛选的随机获取
        random_genes_filtered = await repository.get_random_genes(
            count=2,
            emotion_tag="情感1"
        )
        assert len(random_genes_filtered) <= 2
        assert all(gene.emotion_tag == "情感1" for gene in random_genes_filtered)
        
        log_info("测试", "获取随机情感基因测试通过")
    
    async def test_get_high_quality_genes(self, repository: EmotionalGeneRepository, sample_gene_data: Dict[str, Any]):
        """测试获取高质量情感基因"""
        log_debug("测试", "开始测试获取高质量情感基因")
        
        # 创建不同质量的基因
        high_quality_data = sample_gene_data.copy()
        high_quality_data["intensity_score"] = 0.9
        high_quality_data["reliability_score"] = 0.8
        
        low_quality_data = sample_gene_data.copy()
        low_quality_data["intensity_score"] = 0.3
        low_quality_data["reliability_score"] = 0.2
        
        high_gene = await repository.create_gene(high_quality_data)
        low_gene = await repository.create_gene(low_quality_data)
        
        # 获取高质量基因
        high_quality_genes, total = await repository.get_high_quality_genes(threshold=0.7)
        
        # 验证结果
        assert total >= 1
        assert all(gene.quality_score >= 0.7 for gene in high_quality_genes)
        assert any(gene.id == high_gene.id for gene in high_quality_genes)
        assert not any(gene.id == low_gene.id for gene in high_quality_genes)
        
        log_info("测试", "获取高质量情感基因测试通过")
    
    async def test_update_gene(self, repository: EmotionalGeneRepository, sample_gene_data: Dict[str, Any]):
        """测试更新情感基因"""
        log_debug("测试", "开始测试更新情感基因")
        
        # 创建基因
        gene = await repository.create_gene(sample_gene_data)
        original_updated_at = gene.updated_at
        
        # 更新数据
        update_data = {
            "emotion_tag": "更新后的恐惧",
            "intensity_score": 0.95,
            "category": "更新后的类别"
        }
        
        # 执行更新
        updated_gene = await repository.update_gene(gene.id, update_data)
        
        # 验证更新结果
        assert updated_gene is not None
        assert updated_gene.emotion_tag == "更新后的恐惧"
        assert updated_gene.intensity_score == 0.95
        assert updated_gene.category == "更新后的类别"
        assert updated_gene.updated_at > original_updated_at
        
        # 测试更新不存在的基因
        non_existent_update = await repository.update_gene(99999, update_data)
        assert non_existent_update is None
        
        log_info("测试", "更新情感基因测试通过")
    
    async def test_delete_gene(self, repository: EmotionalGeneRepository, sample_gene_data: Dict[str, Any]):
        """测试删除情感基因"""
        log_debug("测试", "开始测试删除情感基因")
        
        # 创建基因
        gene = await repository.create_gene(sample_gene_data)
        gene_id = gene.id
        
        # 删除基因
        delete_result = await repository.delete_gene(gene_id)
        assert delete_result is True
        
        # 验证删除结果
        deleted_gene = await repository.get_gene_by_id(gene_id)
        assert deleted_gene is None
        
        # 测试删除不存在的基因
        non_existent_delete = await repository.delete_gene(99999)
        assert non_existent_delete is False
        
        log_info("测试", "删除情感基因测试通过")
    
    async def test_increment_usage(self, repository: EmotionalGeneRepository, sample_gene_data: Dict[str, Any]):
        """测试增加基因使用次数"""
        log_debug("测试", "开始测试增加基因使用次数")
        
        # 创建基因
        gene = await repository.create_gene(sample_gene_data)
        original_usage_count = gene.usage_count
        
        # 增加使用次数
        result = await repository.increment_usage(gene.id)
        assert result is True
        
        # 验证使用次数增加
        updated_gene = await repository.get_gene_by_id(gene.id)
        assert updated_gene.usage_count == original_usage_count + 1
        assert updated_gene.last_used_at is not None
        
        # 测试不存在的基因
        non_existent_result = await repository.increment_usage(99999)
        assert non_existent_result is False
        
        log_info("测试", "增加基因使用次数测试通过")
    
    async def test_get_statistics(self, repository: EmotionalGeneRepository, sample_gene_data: Dict[str, Any]):
        """测试获取统计信息"""
        log_debug("测试", "开始测试获取统计信息")
        
        # 创建多个不同类型的基因
        emotions = ["恐惧", "愤怒", "喜悦", "悲伤"]
        qualities = [0.9, 0.7, 0.5, 0.3]
        
        for i, (emotion, quality) in enumerate(zip(emotions, qualities)):
            gene_data = sample_gene_data.copy()
            gene_data["emotion_tag"] = emotion
            gene_data["intensity_score"] = quality
            gene_data["reliability_score"] = quality
            gene_data["usage_count"] = i + 1
            await repository.create_gene(gene_data)
        
        # 获取统计信息
        stats = await repository.get_statistics()
        
        # 验证统计结果
        assert isinstance(stats, EmotionalGeneStats)
        assert stats.total_genes >= 4
        assert len(stats.emotion_distribution) >= 4
        assert stats.average_quality > 0.0
        assert stats.average_intensity > 0.0
        assert stats.average_reliability > 0.0
        assert stats.high_quality_count >= 1  # 至少有一个高质量基因
        
        # 验证情感分布
        for emotion in emotions:
            assert emotion in stats.emotion_distribution
            assert stats.emotion_distribution[emotion] >= 1
        
        log_info("测试", "获取统计信息测试通过", 总基因数=stats.total_genes)


class TestEmotionalGeneSeeder:
    """🧬 [基因填充器测试] 测试情感基因填充工具"""

    @pytest.fixture
    async def test_session(self):
        """创建测试数据库会话"""
        import tempfile
        from pathlib import Path
        from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
        from app.core.database import Base

        # 创建临时数据库
        temp_dir = tempfile.mkdtemp()
        temp_db_path = Path(temp_dir) / "test_seeder.db"
        db_url = f"sqlite+aiosqlite:///{temp_db_path}"

        log_debug("数据库", "创建基因填充器测试数据库", 路径=str(temp_db_path))

        # 创建引擎和会话
        engine = create_async_engine(db_url)
        async_session_maker = async_sessionmaker(bind=engine, class_=AsyncSession)

        # 创建表结构
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)

        # 提供会话
        async with async_session_maker() as session:
            yield session

        # 清理
        await engine.dispose()
        try:
            if temp_db_path.exists():
                temp_db_path.unlink()
        except PermissionError:
            pass

    @pytest.fixture
    async def seeder(self, test_session):
        """创建基因填充器实例"""
        repository = EmotionalGeneRepository(test_session)
        return EmotionalGeneSeeder(repository)
    
    async def test_seed_from_dna_list(self, seeder: EmotionalGeneSeeder):
        """测试从DNA列表批量填充"""
        log_debug("测试", "开始测试从DNA列表批量填充")
        
        # 准备测试DNA数据
        dna_list = [
            {
                "emotion_tag": "恐惧",
                "source_text": "他感到恐惧",
                "physiological_reactions": ["心跳加速"],
                "sensory_triggers": ["看到阴影"],
                "entropy_items": ["环境噪音"],
                "intensity_score": 0.8,
                "reliability_score": 0.7,
                "purification_level": "basic"
            },
            {
                "emotion_tag": "愤怒",
                "source_text": "她感到愤怒",
                "physiological_reactions": ["脸色发红"],
                "sensory_triggers": ["听到侮辱"],
                "entropy_items": ["周围嘈杂"],
                "intensity_score": 0.9,
                "reliability_score": 0.8,
                "purification_level": "basic"
            }
        ]
        
        # 添加元数据
        metadata = {
            "source_type": "测试数据",
            "source_author": "测试作者",
            "category": "测试类别"
        }
        
        # 执行批量填充
        success_count = await seeder.seed_from_dna_list(dna_list, **metadata)
        
        # 验证结果
        assert success_count == 2
        
        # 验证数据是否正确插入
        repository = seeder.repository
        fear_genes, _ = await repository.search_genes(emotion_tag="恐惧")
        anger_genes, _ = await repository.search_genes(emotion_tag="愤怒")
        
        assert len(fear_genes) >= 1
        assert len(anger_genes) >= 1
        
        # 验证元数据是否正确添加
        fear_gene = fear_genes[0]
        assert fear_gene.source_type == "测试数据"
        assert fear_gene.source_author == "测试作者"
        assert fear_gene.category == "测试类别"
        
        log_info("测试", "从DNA列表批量填充测试通过", 成功数量=success_count)
