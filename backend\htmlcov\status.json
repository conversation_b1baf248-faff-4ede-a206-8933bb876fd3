{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.10.1", "globals": "1346a8f8cb408891831a97f3b9bac60c", "files": {"z_5f5a17c013354698___init___py": {"hash": "37cee7ec5fed98ebd40ac72eba394a72", "index": {"url": "z_5f5a17c013354698___init___py.html", "file": "app\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_adebc8a9b0574ea2___init___py": {"hash": "37cee7ec5fed98ebd40ac72eba394a72", "index": {"url": "z_adebc8a9b0574ea2___init___py.html", "file": "app\\core\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_adebc8a9b0574ea2_config_py": {"hash": "dcb06200ad40dd22ec0cd83e4a63bb9c", "index": {"url": "z_adebc8a9b0574ea2_config_py.html", "file": "app\\core\\config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 48, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1374716a89f3e08d___init___py": {"hash": "e17b0b383d1bfd49d5121c332e972f80", "index": {"url": "z_1374716a89f3e08d___init___py.html", "file": "app\\models\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_84e85958b078cc7a___init___py": {"hash": "37cee7ec5fed98ebd40ac72eba394a72", "index": {"url": "z_84e85958b078cc7a___init___py.html", "file": "app\\routers\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_84e85958b078cc7a_generation_py": {"hash": "16b232e5941a7bc4c748f585797ed854", "index": {"url": "z_84e85958b078cc7a_generation_py.html", "file": "app\\routers\\generation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 133, "n_excluded": 0, "n_missing": 24, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b4c115836e286174___init___py": {"hash": "37cee7ec5fed98ebd40ac72eba394a72", "index": {"url": "z_b4c115836e286174___init___py.html", "file": "app\\schemas\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b4c115836e286174_generation_py": {"hash": "c01751ed34d97578494257763dc8fb94", "index": {"url": "z_b4c115836e286174_generation_py.html", "file": "app\\schemas\\generation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 99, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4c37ce8615b5aa70___init___py": {"hash": "37cee7ec5fed98ebd40ac72eba394a72", "index": {"url": "z_4c37ce8615b5aa70___init___py.html", "file": "app\\services\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4c37ce8615b5aa70_zhipu_client_py": {"hash": "db33f42945cbd383d7472ef0bdda1222", "index": {"url": "z_4c37ce8615b5aa70_zhipu_client_py.html", "file": "app\\services\\zhipu_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 139, "n_excluded": 0, "n_missing": 16, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a7b07432402c05f1___init___py": {"hash": "e17b0b383d1bfd49d5121c332e972f80", "index": {"url": "z_a7b07432402c05f1___init___py.html", "file": "app\\utils\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}