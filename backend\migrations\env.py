"""
💾 [数据库] Alembic迁移环境配置
配置数据库迁移环境，支持离线和在线模式
"""

import asyncio
import sys
import os
from logging.config import fileConfig
from pathlib import Path

from sqlalchemy import engine_from_config, pool
from sqlalchemy.ext.asyncio import create_async_engine
from alembic import context

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

# 导入应用配置和模型
from app.core.config import settings
from app.core.database import Base
# 导入所有模型以确保它们被注册到Base.metadata
from app.models import StoryBible, Chapter

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# 设置目标元数据为我们的Base.metadata
target_metadata = Base.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """
    💾 [数据库] 离线模式运行迁移

    在离线模式下配置上下文，只使用URL而不创建Engine。
    这样可以在没有DBAPI的情况下生成SQL脚本。
    """
    # 使用配置中的数据库URL
    url = settings.database_url
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        compare_type=True,  # 比较列类型变化
        compare_server_default=True,  # 比较服务器默认值变化
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """
    💾 [数据库] 在线模式运行迁移

    在在线模式下创建Engine并与上下文关联连接。
    支持异步数据库连接。
    """
    # 创建异步引擎
    connectable = create_async_engine(
        settings.database_url,
        poolclass=pool.NullPool,
    )

    def do_run_migrations(connection):
        context.configure(
            connection=connection,
            target_metadata=target_metadata,
            compare_type=True,  # 比较列类型变化
            compare_server_default=True,  # 比较服务器默认值变化
        )

        with context.begin_transaction():
            context.run_migrations()

    async def run_async_migrations():
        async with connectable.connect() as connection:
            await connection.run_sync(do_run_migrations)

        await connectable.dispose()

    # 运行异步迁移
    asyncio.run(run_async_migrations())


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
