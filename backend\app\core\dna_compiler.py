"""
🧬 情感DNA提纯器 (Emotional DNA Compiler)

这个模块负责将情感采矿机提取的原始段落进行"提纯"，转化为结构化的情感DNA。
提纯过程包括：脱壳、脱水、基因配对等算法。

主要功能：
1. 脱壳算法：剔除比喻句，保留物理反应描述
2. 脱水算法：去除抒情词，保留感官数据  
3. 基因配对：将提纯后的数据与情感标签进行结构化映射
4. 熵增生成：为情感DNA添加环境不完美细节
5. AI增强提纯：使用AI服务提升提纯质量

作者: 文心小说后端服务系统
创建时间: 2025-08-02
"""

import re
import json
import random
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum

from app.core.config import settings, log_debug, log_info, log_error


class PurificationLevel(Enum):
    """🔧 提纯等级枚举"""
    BASIC = "basic"      # 基础提纯
    ENHANCED = "enhanced"  # AI增强提纯
    PREMIUM = "premium"   # 高级提纯


@dataclass
class EmotionalDNA:
    """🧬 情感DNA数据结构"""
    emotion_tag: str                    # 情感标签
    physiological_reactions: List[str]  # 生理反应列表
    sensory_triggers: List[str]         # 感官触发器列表
    entropy_items: List[str]            # 熵增项目（环境不完美细节）
    intensity_score: float              # 强度评分 (0.0-1.0)
    reliability_score: float            # 可靠性评分 (0.0-1.0)
    purification_level: str             # 提纯等级
    source_text: str                    # 原始文本
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return asdict(self)


class ShellRemover:
    """🐚 脱壳器 - 剔除比喻句，保留物理反应描述"""
    
    def __init__(self):
        log_debug("脱壳器", "初始化脱壳器")
        
        # 比喻句标识词
        self.metaphor_indicators = [
            "像", "如", "似", "仿佛", "好像", "犹如", "宛如", "恰似",
            "如同", "好比", "正如", "就像", "仿若", "似乎"
        ]
        
        # 物理反应关键词
        self.physical_keywords = [
            "心跳", "呼吸", "脉搏", "血压", "体温", "汗水", "颤抖", "肌肉",
            "瞳孔", "面色", "声音", "手指", "眼泪", "唾液", "胃部", "胸口",
            "喉咙", "双腿", "手掌", "后背", "额头", "嘴唇", "眼睛", "脸颊"
        ]
    
    def remove_metaphors(self, text: str) -> str:
        """移除比喻句"""
        log_debug("脱壳器", "开始移除比喻句", 文本长度=len(text))
        
        sentences = re.split(r'[。！？；]', text)
        filtered_sentences = []
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
                
            # 检查是否包含比喻词
            has_metaphor = any(indicator in sentence for indicator in self.metaphor_indicators)
            
            # 检查是否包含物理反应
            has_physical = any(keyword in sentence for keyword in self.physical_keywords)
            
            # 保留包含物理反应且不是纯比喻的句子
            if has_physical and not (has_metaphor and not has_physical):
                filtered_sentences.append(sentence)
        
        result = '。'.join(filtered_sentences)
        if result and not result.endswith('。'):
            result += '。'
            
        log_info("脱壳器", "比喻句移除完成", 原始句数=len(sentences), 保留句数=len(filtered_sentences))
        return result
    
    def extract_physical_reactions(self, text: str) -> List[str]:
        """提取物理反应描述"""
        log_debug("脱壳器", "开始提取物理反应")
        
        reactions = []
        sentences = re.split(r'[。！？；]', text)
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
                
            # 检查是否包含物理反应关键词
            for keyword in self.physical_keywords:
                if keyword in sentence:
                    reactions.append(sentence)
                    break
        
        log_info("脱壳器", "物理反应提取完成", 提取数量=len(reactions))
        return reactions


class Dehydrator:
    """💧 脱水器 - 去除抒情词，保留感官数据"""
    
    def __init__(self):
        log_debug("脱水器", "初始化脱水器")
        
        # 抒情词汇
        self.lyrical_words = [
            "美丽", "壮观", "震撼", "感动", "激动", "兴奋", "悲伤", "痛苦",
            "幸福", "快乐", "愤怒", "恐惧", "惊讶", "厌恶", "羞耻", "骄傲",
            "嫉妒", "怨恨", "绝望", "希望", "爱", "恨", "思念", "怀念"
        ]
        
        # 感官关键词
        self.sensory_keywords = {
            "视觉": ["看到", "看见", "目睹", "注视", "凝视", "瞥见", "色彩", "光线", "阴影", "明亮", "昏暗"],
            "听觉": ["听到", "听见", "声音", "噪音", "音乐", "歌声", "哭声", "笑声", "呼喊", "低语"],
            "嗅觉": ["闻到", "气味", "香味", "臭味", "芳香", "刺鼻", "清香", "腥味", "烟味"],
            "味觉": ["尝到", "味道", "甜味", "苦味", "酸味", "辣味", "咸味", "鲜味", "涩味"],
            "触觉": ["触摸", "感觉", "温度", "冰冷", "温暖", "粗糙", "光滑", "柔软", "坚硬", "湿润", "干燥"]
        }
    
    def remove_lyrical_words(self, text: str) -> str:
        """移除抒情词汇"""
        log_debug("脱水器", "开始移除抒情词汇", 文本长度=len(text))
        
        result = text
        removed_count = 0
        
        for word in self.lyrical_words:
            if word in result:
                # 简单替换，实际应用中可能需要更复杂的处理
                result = result.replace(word, "")
                removed_count += 1
        
        # 清理多余的空格和标点
        result = re.sub(r'\s+', ' ', result)
        result = re.sub(r'[，,]\s*[，,]', '，', result)
        result = result.strip()
        
        log_info("脱水器", "抒情词汇移除完成", 移除词数=removed_count)
        return result
    
    def extract_sensory_data(self, text: str) -> Dict[str, List[str]]:
        """提取感官数据"""
        log_debug("脱水器", "开始提取感官数据")
        
        sensory_data = {category: [] for category in self.sensory_keywords.keys()}
        sentences = re.split(r'[。！？；]', text)
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
                
            for category, keywords in self.sensory_keywords.items():
                for keyword in keywords:
                    if keyword in sentence:
                        sensory_data[category].append(sentence)
                        break
        
        # 移除空的类别
        sensory_data = {k: v for k, v in sensory_data.items() if v}
        
        total_count = sum(len(v) for v in sensory_data.values())
        log_info("脱水器", "感官数据提取完成", 总数量=total_count, 类别数=len(sensory_data))
        return sensory_data


class GenePairer:
    """🧬 基因配对器 - 将提纯后的数据与情感标签进行结构化映射"""
    
    def __init__(self):
        log_debug("基因配对器", "初始化基因配对器")
        
        # 情感强度评估关键词
        self.intensity_keywords = {
            "高强度": ["剧烈", "强烈", "激烈", "猛烈", "疯狂", "极度", "严重", "巨大"],
            "中强度": ["明显", "清楚", "显著", "较强", "相当", "不少", "一定"],
            "低强度": ["轻微", "微弱", "淡淡", "稍微", "略微", "一点", "些许"]
        }
    
    def calculate_intensity_score(self, text: str) -> float:
        """计算情感强度评分"""
        log_debug("基因配对器", "开始计算情感强度评分")
        
        score = 0.5  # 基础分数
        
        for level, keywords in self.intensity_keywords.items():
            for keyword in keywords:
                if keyword in text:
                    if level == "高强度":
                        score += 0.3
                    elif level == "中强度":
                        score += 0.1
                    elif level == "低强度":
                        score -= 0.1
        
        # 限制在0.0-1.0范围内
        score = max(0.0, min(1.0, score))
        
        log_info("基因配对器", "情感强度评分完成", 评分=score)
        return score
    
    def calculate_reliability_score(self, physical_reactions: List[str], sensory_data: Dict[str, List[str]]) -> float:
        """计算可靠性评分"""
        log_debug("基因配对器", "开始计算可靠性评分")
        
        # 基于物理反应和感官数据的数量和质量评估
        physical_score = min(len(physical_reactions) * 0.2, 0.6)
        sensory_score = min(len(sensory_data) * 0.1, 0.4)
        
        total_score = physical_score + sensory_score
        
        log_info("基因配对器", "可靠性评分完成", 评分=total_score)
        return total_score
    
    def pair_genes(self, emotion_tag: str, physical_reactions: List[str], 
                   sensory_data: Dict[str, List[str]], source_text: str) -> EmotionalDNA:
        """执行基因配对，生成情感DNA"""
        log_debug("基因配对器", "开始基因配对", 情感标签=emotion_tag)
        
        # 合并感官数据
        sensory_triggers = []
        for category, triggers in sensory_data.items():
            sensory_triggers.extend(triggers)
        
        # 计算评分
        intensity_score = self.calculate_intensity_score(source_text)
        reliability_score = self.calculate_reliability_score(physical_reactions, sensory_data)
        
        # 生成熵增项目（暂时为空，后续实现）
        entropy_items = []
        
        dna = EmotionalDNA(
            emotion_tag=emotion_tag,
            physiological_reactions=physical_reactions,
            sensory_triggers=sensory_triggers,
            entropy_items=entropy_items,
            intensity_score=intensity_score,
            reliability_score=reliability_score,
            purification_level=PurificationLevel.BASIC.value,
            source_text=source_text
        )
        
        log_info("基因配对器", "基因配对完成", 情感标签=emotion_tag, 强度评分=intensity_score, 可靠性评分=reliability_score)
        return dna


class EntropyGenerator:
    """🌀 熵增生成器 - 为情感DNA添加环境不完美细节"""
    
    def __init__(self):
        log_debug("熵增生成器", "初始化熵增生成器")
        
        # 环境不完美细节模板
        self.entropy_templates = [
            "周围传来{noise}的声音",
            "空气中弥漫着{smell}的气味", 
            "光线{light_condition}，影响了视线",
            "温度{temperature}，让人感到不适",
            "地面{surface_condition}，影响了站立",
            "风{wind_condition}，带来了干扰"
        ]
        
        self.noise_options = ["嘈杂", "刺耳", "低沉", "断续", "模糊"]
        self.smell_options = ["刺鼻", "腥臭", "霉味", "烟味", "化学"]
        self.light_options = ["昏暗", "刺眼", "闪烁", "不稳定", "偏暗"]
        self.temperature_options = ["过热", "过冷", "闷热", "湿冷", "干燥"]
        self.surface_options = ["不平", "湿滑", "松软", "颠簸", "倾斜"]
        self.wind_options = ["过强", "忽大忽小", "带着尘土", "刺骨", "闷热"]
    
    def generate_entropy_items(self, count: int = 2) -> List[str]:
        """生成熵增项目"""
        log_debug("熵增生成器", "开始生成熵增项目", 数量=count)
        
        entropy_items = []
        
        for _ in range(count):
            template = random.choice(self.entropy_templates)
            
            # 根据模板填充具体内容
            if "{noise}" in template:
                item = template.format(noise=random.choice(self.noise_options))
            elif "{smell}" in template:
                item = template.format(smell=random.choice(self.smell_options))
            elif "{light_condition}" in template:
                item = template.format(light_condition=random.choice(self.light_options))
            elif "{temperature}" in template:
                item = template.format(temperature=random.choice(self.temperature_options))
            elif "{surface_condition}" in template:
                item = template.format(surface_condition=random.choice(self.surface_options))
            elif "{wind_condition}" in template:
                item = template.format(wind_condition=random.choice(self.wind_options))
            else:
                item = template
                
            entropy_items.append(item)
        
        log_info("熵增生成器", "熵增项目生成完成", 生成数量=len(entropy_items))
        return entropy_items


class DNACompiler:
    """🧬 情感DNA编译器 - 主要编译器类"""
    
    def __init__(self):
        log_debug("DNA编译器", "初始化情感DNA编译器")
        
        self.shell_remover = ShellRemover()
        self.dehydrator = Dehydrator()
        self.gene_pairer = GenePairer()
        self.entropy_generator = EntropyGenerator()
        
        # 编译统计
        self.compilation_stats = {
            "total_compiled": 0,
            "successful_compilations": 0,
            "failed_compilations": 0,
            "average_intensity": 0.0,
            "average_reliability": 0.0
        }
        
        log_info("DNA编译器", "情感DNA编译器初始化完成")
    
    def compile_basic(self, emotion_tag: str, raw_text: str) -> EmotionalDNA:
        """基础编译流程"""
        log_debug("DNA编译器", "开始基础编译", 情感标签=emotion_tag, 文本长度=len(raw_text))
        
        try:
            # 步骤1: 脱壳 - 移除比喻句，提取物理反应
            deshelled_text = self.shell_remover.remove_metaphors(raw_text)
            physical_reactions = self.shell_remover.extract_physical_reactions(deshelled_text)
            
            # 步骤2: 脱水 - 移除抒情词，提取感官数据
            dehydrated_text = self.dehydrator.remove_lyrical_words(deshelled_text)
            sensory_data = self.dehydrator.extract_sensory_data(dehydrated_text)
            
            # 步骤3: 基因配对 - 生成结构化DNA
            dna = self.gene_pairer.pair_genes(emotion_tag, physical_reactions, sensory_data, raw_text)
            
            # 步骤4: 添加熵增项目
            entropy_items = self.entropy_generator.generate_entropy_items()
            dna.entropy_items = entropy_items
            
            # 更新统计
            self._update_stats(dna, success=True)
            
            log_info("DNA编译器", "基础编译完成", 情感标签=emotion_tag, 强度评分=dna.intensity_score)
            return dna
            
        except Exception as e:
            log_error("DNA编译器", "基础编译失败", error=e, 情感标签=emotion_tag)
            self._update_stats(None, success=False)
            raise
    
    def _update_stats(self, dna: Optional[EmotionalDNA], success: bool):
        """更新编译统计"""
        self.compilation_stats["total_compiled"] += 1
        
        if success and dna:
            self.compilation_stats["successful_compilations"] += 1
            
            # 更新平均值
            total_success = self.compilation_stats["successful_compilations"]
            current_avg_intensity = self.compilation_stats["average_intensity"]
            current_avg_reliability = self.compilation_stats["average_reliability"]
            
            self.compilation_stats["average_intensity"] = (
                (current_avg_intensity * (total_success - 1) + dna.intensity_score) / total_success
            )
            self.compilation_stats["average_reliability"] = (
                (current_avg_reliability * (total_success - 1) + dna.reliability_score) / total_success
            )
        else:
            self.compilation_stats["failed_compilations"] += 1
    
    def get_compilation_stats(self) -> Dict[str, Any]:
        """获取编译统计信息"""
        return self.compilation_stats.copy()
