"""
🧬 情感基因仓库

提供情感基因数据库的CRUD操作和高级检索功能。

主要功能：
1. 基础CRUD操作
2. 高级检索和筛选
3. 统计分析功能
4. 批量操作支持
5. 质量评估和排序

作者: 文心小说后端服务系统
创建时间: 2025-08-02
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from sqlalchemy import and_, or_, desc, asc, func, text, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.models.emotional_gene import EmotionalGene, EmotionalGeneStats
from app.core.config import log_debug, log_info, log_error


class EmotionalGeneRepository:
    """🧬 情感基因仓库类"""
    
    def __init__(self, session: AsyncSession):
        self.session = session
        log_debug("情感基因仓库", "初始化情感基因仓库")
    
    async def create_gene(self, gene_data: Dict[str, Any]) -> EmotionalGene:
        """创建新的情感基因"""
        log_debug("情感基因仓库", "开始创建情感基因", 情感标签=gene_data.get("emotion_tag"))
        
        try:
            gene = EmotionalGene(**gene_data)
            gene.update_quality_score()  # 计算质量评分
            
            self.session.add(gene)
            await self.session.commit()
            await self.session.refresh(gene)
            
            log_info("情感基因仓库", "情感基因创建成功", 基因ID=gene.id, 情感标签=gene.emotion_tag)
            return gene
            
        except Exception as e:
            await self.session.rollback()
            log_error("情感基因仓库", "创建情感基因失败", error=e)
            raise
    
    async def get_gene_by_id(self, gene_id: int) -> Optional[EmotionalGene]:
        """根据ID获取情感基因"""
        log_debug("情感基因仓库", "查询情感基因", 基因ID=gene_id)
        
        try:
            result = await self.session.get(EmotionalGene, gene_id)
            
            if result:
                log_info("情感基因仓库", "情感基因查询成功", 基因ID=gene_id)
            else:
                log_debug("情感基因仓库", "情感基因不存在", 基因ID=gene_id)
            
            return result
            
        except Exception as e:
            log_error("情感基因仓库", "查询情感基因失败", error=e, 基因ID=gene_id)
            raise
    
    async def search_genes(
        self,
        emotion_tag: Optional[str] = None,
        category: Optional[str] = None,
        min_quality: Optional[float] = None,
        min_intensity: Optional[float] = None,
        min_reliability: Optional[float] = None,
        purification_level: Optional[str] = None,
        source_type: Optional[str] = None,
        limit: int = 20,
        offset: int = 0,
        order_by: str = "quality_score",
        order_direction: str = "desc"
    ) -> Tuple[List[EmotionalGene], int]:
        """搜索情感基因"""
        log_debug("情感基因仓库", "开始搜索情感基因", 
                 情感标签=emotion_tag, 类别=category, 最小质量=min_quality)
        
        try:
            # 构建查询条件
            conditions = []
            
            if emotion_tag:
                conditions.append(EmotionalGene.emotion_tag.ilike(f"%{emotion_tag}%"))
            
            if category:
                conditions.append(EmotionalGene.category == category)
            
            if min_quality is not None:
                conditions.append(EmotionalGene.quality_score >= min_quality)
            
            if min_intensity is not None:
                conditions.append(EmotionalGene.intensity_score >= min_intensity)
            
            if min_reliability is not None:
                conditions.append(EmotionalGene.reliability_score >= min_reliability)
            
            if purification_level:
                conditions.append(EmotionalGene.purification_level == purification_level)
            
            if source_type:
                conditions.append(EmotionalGene.source_type == source_type)
            
            # 构建查询
            query = select(EmotionalGene)

            if conditions:
                query = query.where(and_(*conditions))

            # 排序
            if order_direction.lower() == "desc":
                query = query.order_by(desc(getattr(EmotionalGene, order_by)))
            else:
                query = query.order_by(asc(getattr(EmotionalGene, order_by)))

            # 获取总数
            count_query = select(func.count(EmotionalGene.id))
            if conditions:
                count_query = count_query.where(and_(*conditions))
            total_count_result = await self.session.execute(count_query)
            total_count = total_count_result.scalar()

            # 分页
            paginated_query = query.offset(offset).limit(limit)
            result = await self.session.execute(paginated_query)
            results = result.scalars().all()
            
            log_info("情感基因仓库", "情感基因搜索完成", 
                    返回数量=len(results), 总数量=total_count)
            
            return results, total_count
            
        except Exception as e:
            log_error("情感基因仓库", "搜索情感基因失败", error=e)
            raise
    
    async def get_random_genes(
        self,
        count: int = 5,
        emotion_tag: Optional[str] = None,
        min_quality: Optional[float] = None
    ) -> List[EmotionalGene]:
        """获取随机情感基因"""
        log_debug("情感基因仓库", "获取随机情感基因", 数量=count, 情感标签=emotion_tag)
        
        try:
            query = select(EmotionalGene)

            # 添加筛选条件
            conditions = []
            if emotion_tag:
                conditions.append(EmotionalGene.emotion_tag == emotion_tag)

            if min_quality is not None:
                conditions.append(EmotionalGene.quality_score >= min_quality)

            if conditions:
                query = query.where(and_(*conditions))

            # 随机排序
            query = query.order_by(func.random()).limit(count)

            result = await self.session.execute(query)
            results = result.scalars().all()
            
            log_info("情感基因仓库", "随机情感基因获取完成", 返回数量=len(results))
            return results
            
        except Exception as e:
            log_error("情感基因仓库", "获取随机情感基因失败", error=e)
            raise
    
    async def get_high_quality_genes(
        self,
        threshold: float = 0.7,
        limit: int = 20,
        offset: int = 0
    ) -> Tuple[List[EmotionalGene], int]:
        """获取高质量情感基因"""
        log_debug("情感基因仓库", "获取高质量情感基因", 阈值=threshold)
        
        try:
            query = select(EmotionalGene).where(
                EmotionalGene.quality_score >= threshold
            ).order_by(desc(EmotionalGene.quality_score))

            # 获取总数
            count_query = select(func.count(EmotionalGene.id)).where(
                EmotionalGene.quality_score >= threshold
            )
            total_count_result = await self.session.execute(count_query)
            total_count = total_count_result.scalar()

            # 分页查询
            paginated_query = query.offset(offset).limit(limit)
            result = await self.session.execute(paginated_query)
            results = result.scalars().all()
            
            log_info("情感基因仓库", "高质量情感基因获取完成", 
                    返回数量=len(results), 总数量=total_count)
            
            return results, total_count
            
        except Exception as e:
            log_error("情感基因仓库", "获取高质量情感基因失败", error=e)
            raise
    
    async def get_genes_by_context(
        self,
        category: str,
        limit: int = 20,
        offset: int = 0
    ) -> Tuple[List[EmotionalGene], int]:
        """按上下文类别获取情感基因"""
        log_debug("情感基因仓库", "按上下文获取情感基因", 类别=category)
        
        try:
            query = select(EmotionalGene).where(
                or_(
                    EmotionalGene.category == category,
                    EmotionalGene.subcategory == category,
                    EmotionalGene.context_tags.contains([category])
                )
            ).order_by(desc(EmotionalGene.quality_score))

            # 获取总数
            count_query = select(func.count(EmotionalGene.id)).where(
                or_(
                    EmotionalGene.category == category,
                    EmotionalGene.subcategory == category,
                    EmotionalGene.context_tags.contains([category])
                )
            )
            total_count_result = await self.session.execute(count_query)
            total_count = total_count_result.scalar()

            # 分页查询
            paginated_query = query.offset(offset).limit(limit)
            result = await self.session.execute(paginated_query)
            results = result.scalars().all()
            
            log_info("情感基因仓库", "按上下文获取情感基因完成", 
                    类别=category, 返回数量=len(results))
            
            return results, total_count
            
        except Exception as e:
            log_error("情感基因仓库", "按上下文获取情感基因失败", error=e)
            raise
    
    async def update_gene(self, gene_id: int, update_data: Dict[str, Any]) -> Optional[EmotionalGene]:
        """更新情感基因"""
        log_debug("情感基因仓库", "更新情感基因", 基因ID=gene_id)
        
        try:
            gene = await self.get_gene_by_id(gene_id)
            if not gene:
                log_debug("情感基因仓库", "要更新的情感基因不存在", 基因ID=gene_id)
                return None
            
            # 更新字段
            for key, value in update_data.items():
                if hasattr(gene, key):
                    setattr(gene, key, value)
            
            # 重新计算质量评分
            gene.update_quality_score()
            gene.updated_at = datetime.utcnow()
            
            await self.session.commit()
            await self.session.refresh(gene)
            
            log_info("情感基因仓库", "情感基因更新成功", 基因ID=gene_id)
            return gene
            
        except Exception as e:
            await self.session.rollback()
            log_error("情感基因仓库", "更新情感基因失败", error=e, 基因ID=gene_id)
            raise
    
    async def delete_gene(self, gene_id: int) -> bool:
        """删除情感基因"""
        log_debug("情感基因仓库", "删除情感基因", 基因ID=gene_id)
        
        try:
            gene = await self.get_gene_by_id(gene_id)
            if not gene:
                log_debug("情感基因仓库", "要删除的情感基因不存在", 基因ID=gene_id)
                return False
            
            await self.session.delete(gene)
            await self.session.commit()
            
            log_info("情感基因仓库", "情感基因删除成功", 基因ID=gene_id)
            return True
            
        except Exception as e:
            await self.session.rollback()
            log_error("情感基因仓库", "删除情感基因失败", error=e, 基因ID=gene_id)
            raise
    
    async def increment_usage(self, gene_id: int) -> bool:
        """增加基因使用次数"""
        log_debug("情感基因仓库", "增加基因使用次数", 基因ID=gene_id)
        
        try:
            gene = await self.get_gene_by_id(gene_id)
            if not gene:
                return False

            # 手动更新使用次数和时间
            gene.usage_count += 1
            gene.last_used_at = datetime.utcnow()

            await self.session.commit()
            await self.session.refresh(gene)

            log_info("情感基因仓库", "基因使用次数更新成功", 基因ID=gene_id, 使用次数=gene.usage_count)
            return True
            
        except Exception as e:
            await self.session.rollback()
            log_error("情感基因仓库", "更新基因使用次数失败", error=e, 基因ID=gene_id)
            raise
    
    async def get_statistics(self) -> EmotionalGeneStats:
        """获取情感基因库统计信息"""
        log_debug("情感基因仓库", "开始获取统计信息")
        
        try:
            stats = EmotionalGeneStats()
            
            # 总数量
            count_query = select(func.count(EmotionalGene.id))
            total_result = await self.session.execute(count_query)
            stats.total_genes = total_result.scalar()

            if stats.total_genes == 0:
                log_info("情感基因仓库", "情感基因库为空")
                return stats

            # 情感分布
            emotion_dist_query = select(
                EmotionalGene.emotion_tag,
                func.count(EmotionalGene.id)
            ).group_by(EmotionalGene.emotion_tag)
            emotion_dist_result = await self.session.execute(emotion_dist_query)
            emotion_dist = emotion_dist_result.all()
            stats.emotion_distribution = {tag: count for tag, count in emotion_dist}
            
            # 质量分布
            quality_ranges = [
                ("极高 (0.8-1.0)", 0.8, 1.0),
                ("高 (0.6-0.8)", 0.6, 0.8),
                ("中等 (0.4-0.6)", 0.4, 0.6),
                ("低 (0.2-0.4)", 0.2, 0.4),
                ("极低 (0.0-0.2)", 0.0, 0.2)
            ]
            
            for label, min_val, max_val in quality_ranges:
                quality_count_query = select(func.count(EmotionalGene.id)).where(
                    and_(
                        EmotionalGene.quality_score >= min_val,
                        EmotionalGene.quality_score < max_val
                    )
                )
                quality_count_result = await self.session.execute(quality_count_query)
                count = quality_count_result.scalar()
                stats.quality_distribution[label] = count

            # 平均值
            avg_query = select(
                func.avg(EmotionalGene.quality_score),
                func.avg(EmotionalGene.intensity_score),
                func.avg(EmotionalGene.reliability_score)
            )
            avg_result_obj = await self.session.execute(avg_query)
            avg_result = avg_result_obj.first()
            
            if avg_result:
                stats.average_quality = float(avg_result[0] or 0)
                stats.average_intensity = float(avg_result[1] or 0)
                stats.average_reliability = float(avg_result[2] or 0)
            
            # 高质量基因数量
            high_quality_query = select(func.count(EmotionalGene.id)).where(
                EmotionalGene.quality_score >= 0.7
            )
            high_quality_result = await self.session.execute(high_quality_query)
            stats.high_quality_count = high_quality_result.scalar()

            # 最近添加的基因数量（7天内）
            week_ago = datetime.utcnow() - timedelta(days=7)
            recent_query = select(func.count(EmotionalGene.id)).where(
                EmotionalGene.created_at >= week_ago
            )
            recent_result = await self.session.execute(recent_query)
            stats.recent_additions = recent_result.scalar()

            # 最常用的情感
            most_used_query = select(
                EmotionalGene.emotion_tag,
                func.sum(EmotionalGene.usage_count)
            ).group_by(EmotionalGene.emotion_tag).order_by(
                desc(func.sum(EmotionalGene.usage_count))
            ).limit(5)
            most_used_result = await self.session.execute(most_used_query)
            most_used = most_used_result.all()
            stats.most_used_emotions = [{"emotion": tag, "usage_count": count} for tag, count in most_used]
            
            log_info("情感基因仓库", "统计信息获取完成", 总基因数=stats.total_genes)
            return stats

        except Exception as e:
            log_error("情感基因仓库", "获取统计信息失败", error=e)
            raise


class EmotionalGeneSeeder:
    """🧬 情感基因库填充工具"""

    def __init__(self, repository: EmotionalGeneRepository):
        self.repository = repository
        log_debug("基因填充器", "初始化情感基因填充工具")

    async def seed_from_dna_list(self, dna_list: List[Dict[str, Any]], **metadata) -> int:
        """从DNA列表批量填充基因库"""
        log_debug("基因填充器", "开始批量填充基因库", DNA数量=len(dna_list))

        success_count = 0

        for i, dna_data in enumerate(dna_list):
            try:
                # 添加元数据
                gene_data = dna_data.copy()
                gene_data.update(metadata)

                # 创建基因
                gene = await self.repository.create_gene(gene_data)
                success_count += 1

                log_debug("基因填充器", f"基因创建成功 ({i+1}/{len(dna_list)})",
                         基因ID=gene.id, 情感标签=gene.emotion_tag)

            except Exception as e:
                log_error("基因填充器", f"基因创建失败 ({i+1}/{len(dna_list)})",
                         error=e, 情感标签=dna_data.get("emotion_tag"))
                continue

        log_info("基因填充器", "批量填充完成", 成功数量=success_count, 总数量=len(dna_list))
        return success_count
