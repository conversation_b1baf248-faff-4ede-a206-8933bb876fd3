"""
🧬 情感基因数据库模型

定义情感基因库的数据库表结构，用于存储提纯后的情感DNA。

主要功能：
1. EmotionalGene模型：存储情感DNA的完整信息
2. 支持JSON字段存储复杂数据结构
3. 索引优化查询性能
4. 时间戳跟踪创建和更新时间

作者: 文心小说后端服务系统
创建时间: 2025-08-02
"""

from datetime import datetime
from typing import Dict, List, Any, Optional
from sqlalchemy import Column, String, Text, Float, DateTime, Integer, Index
from sqlalchemy.dialects.sqlite import JSON
from sqlalchemy.ext.declarative import declarative_base

from app.core.database import Base


class EmotionalGene(Base):
    """🧬 情感基因模型 - 存储提纯后的情感DNA"""
    
    __tablename__ = "emotional_genes"
    
    # 主键
    id = Column(Integer, primary_key=True, index=True, comment="情感基因唯一标识")
    
    # 基本信息
    emotion_tag = Column(String(100), nullable=False, index=True, comment="情感标签")
    source_text = Column(Text, nullable=False, comment="原始文本")
    
    # 情感DNA核心数据 (JSON格式存储)
    physiological_reactions = Column(JSON, nullable=False, comment="生理反应列表")
    sensory_triggers = Column(JSON, nullable=False, comment="感官触发器列表")
    entropy_items = Column(JSON, nullable=False, comment="熵增项目列表")
    
    # 质量评估指标
    intensity_score = Column(Float, nullable=False, default=0.0, comment="强度评分 (0.0-1.0)")
    reliability_score = Column(Float, nullable=False, default=0.0, comment="可靠性评分 (0.0-1.0)")
    quality_score = Column(Float, nullable=False, default=0.0, comment="综合质量评分 (0.0-1.0)")
    
    # 提纯信息
    purification_level = Column(String(50), nullable=False, default="basic", comment="提纯等级")
    purification_method = Column(String(100), nullable=True, comment="提纯方法")
    
    # 来源信息
    source_type = Column(String(50), nullable=True, comment="来源类型 (文学作品/真实故事等)")
    source_author = Column(String(200), nullable=True, comment="来源作者")
    source_title = Column(String(500), nullable=True, comment="来源标题")
    
    # 分类标签
    category = Column(String(100), nullable=True, index=True, comment="情感类别")
    subcategory = Column(String(100), nullable=True, comment="情感子类别")
    context_tags = Column(JSON, nullable=True, comment="上下文标签列表")
    
    # 使用统计
    usage_count = Column(Integer, nullable=False, default=0, comment="使用次数")
    last_used_at = Column(DateTime, nullable=True, comment="最后使用时间")
    
    # 时间戳
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    
    # 索引定义
    __table_args__ = (
        Index('idx_emotion_tag_quality', 'emotion_tag', 'quality_score'),
        Index('idx_category_intensity', 'category', 'intensity_score'),
        Index('idx_reliability_created', 'reliability_score', 'created_at'),
        Index('idx_purification_level', 'purification_level'),
        Index('idx_source_type', 'source_type'),
        Index('idx_usage_stats', 'usage_count', 'last_used_at'),
    )
    
    def __repr__(self) -> str:
        return f"<EmotionalGene(id={self.id}, emotion_tag='{self.emotion_tag}', quality_score={self.quality_score})>"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "emotion_tag": self.emotion_tag,
            "source_text": self.source_text,
            "physiological_reactions": self.physiological_reactions,
            "sensory_triggers": self.sensory_triggers,
            "entropy_items": self.entropy_items,
            "intensity_score": self.intensity_score,
            "reliability_score": self.reliability_score,
            "quality_score": self.quality_score,
            "purification_level": self.purification_level,
            "purification_method": self.purification_method,
            "source_type": self.source_type,
            "source_author": self.source_author,
            "source_title": self.source_title,
            "category": self.category,
            "subcategory": self.subcategory,
            "context_tags": self.context_tags,
            "usage_count": self.usage_count,
            "last_used_at": self.last_used_at.isoformat() if self.last_used_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
    
    def calculate_quality_score(self) -> float:
        """计算综合质量评分"""
        # 基于强度评分和可靠性评分计算综合质量
        # 可以根据需要调整权重
        intensity_weight = 0.4
        reliability_weight = 0.6
        
        quality = (self.intensity_score * intensity_weight + 
                  self.reliability_score * reliability_weight)
        
        # 根据生理反应和感官触发器的数量进行调整
        reaction_count = len(self.physiological_reactions) if self.physiological_reactions else 0
        trigger_count = len(self.sensory_triggers) if self.sensory_triggers else 0
        entropy_count = len(self.entropy_items) if self.entropy_items else 0
        
        # 数据丰富度加分
        richness_bonus = min((reaction_count + trigger_count + entropy_count) * 0.05, 0.2)
        quality += richness_bonus
        
        # 限制在0.0-1.0范围内
        return max(0.0, min(1.0, quality))
    
    def update_quality_score(self):
        """更新质量评分"""
        self.quality_score = self.calculate_quality_score()
    
    def increment_usage(self):
        """增加使用次数"""
        self.usage_count += 1
        self.last_used_at = datetime.utcnow()
    
    def is_high_quality(self, threshold: float = 0.7) -> bool:
        """判断是否为高质量基因"""
        return self.quality_score >= threshold
    
    def get_emotion_intensity_level(self) -> str:
        """获取情感强度等级"""
        if self.intensity_score >= 0.8:
            return "极高"
        elif self.intensity_score >= 0.6:
            return "高"
        elif self.intensity_score >= 0.4:
            return "中等"
        elif self.intensity_score >= 0.2:
            return "低"
        else:
            return "极低"
    
    def get_reliability_level(self) -> str:
        """获取可靠性等级"""
        if self.reliability_score >= 0.8:
            return "极可靠"
        elif self.reliability_score >= 0.6:
            return "可靠"
        elif self.reliability_score >= 0.4:
            return "一般"
        elif self.reliability_score >= 0.2:
            return "较低"
        else:
            return "不可靠"
    
    @classmethod
    def from_emotional_dna(cls, dna_data: Dict[str, Any], **kwargs) -> "EmotionalGene":
        """从情感DNA数据创建情感基因实例"""
        gene = cls(
            emotion_tag=dna_data.get("emotion_tag", ""),
            source_text=dna_data.get("source_text", ""),
            physiological_reactions=dna_data.get("physiological_reactions", []),
            sensory_triggers=dna_data.get("sensory_triggers", []),
            entropy_items=dna_data.get("entropy_items", []),
            intensity_score=dna_data.get("intensity_score", 0.0),
            reliability_score=dna_data.get("reliability_score", 0.0),
            purification_level=dna_data.get("purification_level", "basic"),
            **kwargs
        )
        
        # 计算质量评分
        gene.update_quality_score()
        
        return gene


class EmotionalGeneStats:
    """🧬 情感基因库统计信息"""
    
    def __init__(self):
        self.total_genes = 0
        self.emotion_distribution = {}
        self.quality_distribution = {}
        self.purification_distribution = {}
        self.average_quality = 0.0
        self.average_intensity = 0.0
        self.average_reliability = 0.0
        self.high_quality_count = 0
        self.recent_additions = 0
        self.most_used_emotions = []
        self.source_type_distribution = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "total_genes": self.total_genes,
            "emotion_distribution": self.emotion_distribution,
            "quality_distribution": self.quality_distribution,
            "purification_distribution": self.purification_distribution,
            "average_quality": self.average_quality,
            "average_intensity": self.average_intensity,
            "average_reliability": self.average_reliability,
            "high_quality_count": self.high_quality_count,
            "high_quality_percentage": (self.high_quality_count / self.total_genes * 100) if self.total_genes > 0 else 0,
            "recent_additions": self.recent_additions,
            "most_used_emotions": self.most_used_emotions,
            "source_type_distribution": self.source_type_distribution,
        }
