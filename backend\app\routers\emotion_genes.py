"""
🧬 情感基因库API路由模块

提供完整的情感基因库服务API端点：
1. 基因检索和搜索
2. 基因管理操作
3. 统计分析接口
4. 批量操作支持

作者: 文心小说后端服务系统
创建时间: 2025-08-02
"""

from typing import Dict, List, Optional, Any
from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_database_session
from app.repositories.emotional_gene_repo import EmotionalGeneRepository, EmotionalGeneSeeder
from app.models.emotional_gene import EmotionalGene, EmotionalGeneStats
from app.core.config import log_info, log_debug, log_error

# 创建路由器
router = APIRouter(prefix="/emotion-genes", tags=["情感基因库"])


@router.get("/search", response_model=Dict[str, Any])
async def search_emotion_genes(
    emotion_tag: Optional[str] = Query(None, description="情感标签"),
    category: Optional[str] = Query(None, description="情感类别"),
    min_quality: Optional[float] = Query(None, ge=0.0, le=1.0, description="最低质量分数"),
    min_intensity: Optional[float] = Query(None, ge=0.0, le=1.0, description="最低强度分数"),
    min_reliability: Optional[float] = Query(None, ge=0.0, le=1.0, description="最低可靠性分数"),
    purification_level: Optional[str] = Query(None, description="提纯级别"),
    source_type: Optional[str] = Query(None, description="来源类型"),
    limit: int = Query(20, ge=1, le=100, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    order_by: str = Query("quality_score", description="排序字段"),
    order_direction: str = Query("desc", pattern="^(asc|desc)$", description="排序方向"),
    session: AsyncSession = Depends(get_database_session)
):
    """
    🔍 [搜索] 搜索情感基因
    
    支持多条件组合搜索，返回符合条件的情感基因列表
    """
    log_debug("情感基因API", "开始搜索情感基因", 
             情感标签=emotion_tag, 类别=category, 最低质量=min_quality)
    
    try:
        repository = EmotionalGeneRepository(session)
        
        # 执行搜索
        genes, total_count = await repository.search_genes(
            emotion_tag=emotion_tag,
            category=category,
            min_quality=min_quality,
            min_intensity=min_intensity,
            min_reliability=min_reliability,
            purification_level=purification_level,
            source_type=source_type,
            limit=limit,
            offset=offset,
            order_by=order_by,
            order_direction=order_direction
        )
        
        # 转换为字典格式
        gene_list = []
        for gene in genes:
            gene_dict = {
                "id": gene.id,
                "emotion_tag": gene.emotion_tag,
                "source_text": gene.source_text,
                "physiological_reactions": gene.physiological_reactions,
                "sensory_triggers": gene.sensory_triggers,
                "entropy_items": gene.entropy_items,
                "intensity_score": gene.intensity_score,
                "reliability_score": gene.reliability_score,
                "quality_score": gene.quality_score,
                "purification_level": gene.purification_level,
                "category": gene.category,
                "subcategory": gene.subcategory,
                "source_type": gene.source_type,
                "source_author": gene.source_author,
                "source_title": gene.source_title,
                "usage_count": gene.usage_count,
                "created_at": gene.created_at.isoformat() if gene.created_at else None,
                "updated_at": gene.updated_at.isoformat() if gene.updated_at else None,
                "last_used_at": gene.last_used_at.isoformat() if gene.last_used_at else None
            }
            gene_list.append(gene_dict)
        
        result = {
            "genes": gene_list,
            "total_count": total_count,
            "limit": limit,
            "offset": offset,
            "has_more": offset + limit < total_count
        }
        
        log_info("情感基因API", "搜索情感基因完成", 
                找到数量=len(gene_list), 总数=total_count)
        
        return result
        
    except Exception as e:
        log_error("情感基因API", "搜索情感基因失败", error=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"搜索情感基因失败: {str(e)}"
        )


@router.get("/{gene_id}", response_model=Dict[str, Any])
async def get_emotion_gene(
    gene_id: int,
    session: AsyncSession = Depends(get_database_session)
):
    """
    📖 [获取] 根据ID获取单个情感基因
    """
    log_debug("情感基因API", "获取情感基因详情", 基因ID=gene_id)
    
    try:
        repository = EmotionalGeneRepository(session)
        gene = await repository.get_gene_by_id(gene_id)
        
        if not gene:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"情感基因 {gene_id} 不存在"
            )
        
        # 增加使用次数
        await repository.increment_usage(gene_id)
        
        # 转换为字典格式
        gene_dict = {
            "id": gene.id,
            "emotion_tag": gene.emotion_tag,
            "source_text": gene.source_text,
            "physiological_reactions": gene.physiological_reactions,
            "sensory_triggers": gene.sensory_triggers,
            "entropy_items": gene.entropy_items,
            "intensity_score": gene.intensity_score,
            "reliability_score": gene.reliability_score,
            "quality_score": gene.quality_score,
            "purification_level": gene.purification_level,
            "category": gene.category,
            "subcategory": gene.subcategory,
            "context_tags": gene.context_tags,
            "source_type": gene.source_type,
            "source_author": gene.source_author,
            "source_title": gene.source_title,
            "usage_count": gene.usage_count,
            "created_at": gene.created_at.isoformat() if gene.created_at else None,
            "updated_at": gene.updated_at.isoformat() if gene.updated_at else None,
            "last_used_at": gene.last_used_at.isoformat() if gene.last_used_at else None
        }
        
        log_info("情感基因API", "获取情感基因成功", 基因ID=gene_id, 情感标签=gene.emotion_tag)
        return gene_dict
        
    except HTTPException:
        raise
    except Exception as e:
        log_error("情感基因API", "获取情感基因失败", error=e, 基因ID=gene_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取情感基因失败: {str(e)}"
        )


@router.get("/random/genes", response_model=List[Dict[str, Any]])
async def get_random_emotion_genes(
    count: int = Query(5, ge=1, le=20, description="随机获取数量"),
    emotion_tag: Optional[str] = Query(None, description="指定情感标签"),
    min_quality: Optional[float] = Query(None, ge=0.0, le=1.0, description="最低质量分数"),
    session: AsyncSession = Depends(get_database_session)
):
    """
    🎲 [随机] 随机获取情感基因
    
    用于为AI提供随机的情感基因样本
    """
    log_debug("情感基因API", "随机获取情感基因", 数量=count, 情感标签=emotion_tag)
    
    try:
        repository = EmotionalGeneRepository(session)
        genes = await repository.get_random_genes(
            count=count,
            emotion_tag=emotion_tag,
            min_quality=min_quality
        )
        
        # 转换为字典格式
        gene_list = []
        for gene in genes:
            gene_dict = {
                "id": gene.id,
                "emotion_tag": gene.emotion_tag,
                "source_text": gene.source_text,
                "physiological_reactions": gene.physiological_reactions,
                "sensory_triggers": gene.sensory_triggers,
                "entropy_items": gene.entropy_items,
                "intensity_score": gene.intensity_score,
                "reliability_score": gene.reliability_score,
                "quality_score": gene.quality_score,
                "purification_level": gene.purification_level,
                "category": gene.category,
                "usage_count": gene.usage_count
            }
            gene_list.append(gene_dict)
        
        log_info("情感基因API", "随机获取情感基因完成", 获取数量=len(gene_list))
        return gene_list
        
    except Exception as e:
        log_error("情感基因API", "随机获取情感基因失败", error=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"随机获取情感基因失败: {str(e)}"
        )


@router.get("/high-quality/genes", response_model=Dict[str, Any])
async def get_high_quality_genes(
    threshold: float = Query(0.7, ge=0.0, le=1.0, description="质量阈值"),
    limit: int = Query(20, ge=1, le=100, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    session: AsyncSession = Depends(get_database_session)
):
    """
    ⭐ [高质量] 获取高质量情感基因
    
    返回质量分数超过指定阈值的情感基因
    """
    log_debug("情感基因API", "获取高质量情感基因", 质量阈值=threshold)
    
    try:
        repository = EmotionalGeneRepository(session)
        genes, total_count = await repository.get_high_quality_genes(
            threshold=threshold,
            limit=limit,
            offset=offset
        )
        
        # 转换为字典格式
        gene_list = []
        for gene in genes:
            gene_dict = {
                "id": gene.id,
                "emotion_tag": gene.emotion_tag,
                "source_text": gene.source_text,
                "physiological_reactions": gene.physiological_reactions,
                "sensory_triggers": gene.sensory_triggers,
                "entropy_items": gene.entropy_items,
                "intensity_score": gene.intensity_score,
                "reliability_score": gene.reliability_score,
                "quality_score": gene.quality_score,
                "purification_level": gene.purification_level,
                "category": gene.category,
                "source_type": gene.source_type,
                "usage_count": gene.usage_count
            }
            gene_list.append(gene_dict)
        
        result = {
            "genes": gene_list,
            "total_count": total_count,
            "threshold": threshold,
            "limit": limit,
            "offset": offset,
            "has_more": offset + limit < total_count
        }
        
        log_info("情感基因API", "获取高质量情感基因完成", 
                找到数量=len(gene_list), 总数=total_count, 质量阈值=threshold)
        
        return result
        
    except Exception as e:
        log_error("情感基因API", "获取高质量情感基因失败", error=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取高质量情感基因失败: {str(e)}"
        )


@router.get("/category/{category}", response_model=Dict[str, Any])
async def get_genes_by_category(
    category: str,
    limit: int = Query(20, ge=1, le=100, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    session: AsyncSession = Depends(get_database_session)
):
    """
    📂 [分类] 根据类别获取情感基因

    支持按主类别、子类别或上下文标签搜索
    """
    log_debug("情感基因API", "根据类别获取情感基因", 类别=category)

    try:
        repository = EmotionalGeneRepository(session)
        genes, total_count = await repository.get_genes_by_category(
            category=category,
            limit=limit,
            offset=offset
        )

        # 转换为字典格式
        gene_list = []
        for gene in genes:
            gene_dict = {
                "id": gene.id,
                "emotion_tag": gene.emotion_tag,
                "source_text": gene.source_text,
                "physiological_reactions": gene.physiological_reactions,
                "sensory_triggers": gene.sensory_triggers,
                "entropy_items": gene.entropy_items,
                "intensity_score": gene.intensity_score,
                "reliability_score": gene.reliability_score,
                "quality_score": gene.quality_score,
                "category": gene.category,
                "subcategory": gene.subcategory,
                "context_tags": gene.context_tags,
                "usage_count": gene.usage_count
            }
            gene_list.append(gene_dict)

        result = {
            "genes": gene_list,
            "total_count": total_count,
            "category": category,
            "limit": limit,
            "offset": offset,
            "has_more": offset + limit < total_count
        }

        log_info("情感基因API", "根据类别获取情感基因完成",
                类别=category, 找到数量=len(gene_list), 总数=total_count)

        return result

    except Exception as e:
        log_error("情感基因API", "根据类别获取情感基因失败", error=e, 类别=category)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"根据类别获取情感基因失败: {str(e)}"
        )


@router.get("/statistics/overview", response_model=Dict[str, Any])
async def get_gene_statistics(
    session: AsyncSession = Depends(get_database_session)
):
    """
    📊 [统计] 获取情感基因库统计信息

    返回基因库的整体统计数据和分析报告
    """
    log_debug("情感基因API", "获取情感基因库统计信息")

    try:
        repository = EmotionalGeneRepository(session)
        stats = await repository.get_statistics()

        # 转换为字典格式
        stats_dict = {
            "total_genes": stats.total_genes,
            "emotion_distribution": stats.emotion_distribution,
            "quality_distribution": stats.quality_distribution,
            "average_quality": stats.average_quality,
            "average_intensity": stats.average_intensity,
            "average_reliability": stats.average_reliability,
            "high_quality_count": stats.high_quality_count,
            "recent_additions": stats.recent_additions,
            "most_used_emotions": stats.most_used_emotions,
            "source_type_distribution": stats.source_type_distribution
        }

        log_info("情感基因API", "获取统计信息完成",
                总基因数=stats.total_genes, 高质量基因数=stats.high_quality_count)

        return stats_dict

    except Exception as e:
        log_error("情感基因API", "获取统计信息失败", error=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取统计信息失败: {str(e)}"
        )


@router.post("/create", response_model=Dict[str, Any])
async def create_emotion_gene(
    gene_data: Dict[str, Any],
    session: AsyncSession = Depends(get_database_session)
):
    """
    ➕ [创建] 创建新的情感基因

    接收情感基因数据并存储到数据库
    """
    log_debug("情感基因API", "创建新情感基因", 情感标签=gene_data.get("emotion_tag"))

    try:
        repository = EmotionalGeneRepository(session)
        gene = await repository.create_gene(gene_data)

        # 转换为字典格式
        gene_dict = {
            "id": gene.id,
            "emotion_tag": gene.emotion_tag,
            "source_text": gene.source_text,
            "physiological_reactions": gene.physiological_reactions,
            "sensory_triggers": gene.sensory_triggers,
            "entropy_items": gene.entropy_items,
            "intensity_score": gene.intensity_score,
            "reliability_score": gene.reliability_score,
            "quality_score": gene.quality_score,
            "purification_level": gene.purification_level,
            "category": gene.category,
            "subcategory": gene.subcategory,
            "source_type": gene.source_type,
            "source_author": gene.source_author,
            "source_title": gene.source_title,
            "created_at": gene.created_at.isoformat() if gene.created_at else None
        }

        log_info("情感基因API", "创建情感基因成功",
                基因ID=gene.id, 情感标签=gene.emotion_tag)

        return gene_dict

    except Exception as e:
        log_error("情感基因API", "创建情感基因失败", error=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建情感基因失败: {str(e)}"
        )


@router.put("/{gene_id}", response_model=Dict[str, Any])
async def update_emotion_gene(
    gene_id: int,
    update_data: Dict[str, Any],
    session: AsyncSession = Depends(get_database_session)
):
    """
    ✏️ [更新] 更新情感基因信息
    """
    log_debug("情感基因API", "更新情感基因", 基因ID=gene_id)

    try:
        repository = EmotionalGeneRepository(session)
        gene = await repository.update_gene(gene_id, update_data)

        if not gene:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"情感基因 {gene_id} 不存在"
            )

        # 转换为字典格式
        gene_dict = {
            "id": gene.id,
            "emotion_tag": gene.emotion_tag,
            "source_text": gene.source_text,
            "physiological_reactions": gene.physiological_reactions,
            "sensory_triggers": gene.sensory_triggers,
            "entropy_items": gene.entropy_items,
            "intensity_score": gene.intensity_score,
            "reliability_score": gene.reliability_score,
            "quality_score": gene.quality_score,
            "purification_level": gene.purification_level,
            "category": gene.category,
            "subcategory": gene.subcategory,
            "source_type": gene.source_type,
            "updated_at": gene.updated_at.isoformat() if gene.updated_at else None
        }

        log_info("情感基因API", "更新情感基因成功", 基因ID=gene_id)
        return gene_dict

    except HTTPException:
        raise
    except Exception as e:
        log_error("情感基因API", "更新情感基因失败", error=e, 基因ID=gene_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新情感基因失败: {str(e)}"
        )


@router.delete("/{gene_id}")
async def delete_emotion_gene(
    gene_id: int,
    session: AsyncSession = Depends(get_database_session)
):
    """
    🗑️ [删除] 删除情感基因
    """
    log_debug("情感基因API", "删除情感基因", 基因ID=gene_id)

    try:
        repository = EmotionalGeneRepository(session)
        success = await repository.delete_gene(gene_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"情感基因 {gene_id} 不存在"
            )

        log_info("情感基因API", "删除情感基因成功", 基因ID=gene_id)
        return {"message": f"情感基因 {gene_id} 已成功删除"}

    except HTTPException:
        raise
    except Exception as e:
        log_error("情感基因API", "删除情感基因失败", error=e, 基因ID=gene_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除情感基因失败: {str(e)}"
        )


@router.post("/batch/seed", response_model=Dict[str, Any])
async def batch_seed_genes(
    dna_list: List[Dict[str, Any]],
    metadata: Optional[Dict[str, Any]] = None,
    session: AsyncSession = Depends(get_database_session)
):
    """
    📦 [批量] 批量填充情感基因

    接收DNA列表和元数据，批量创建情感基因
    """
    log_debug("情感基因API", "批量填充情感基因", DNA数量=len(dna_list))

    try:
        repository = EmotionalGeneRepository(session)
        seeder = EmotionalGeneSeeder(repository)

        # 执行批量填充
        if metadata:
            success_count = await seeder.seed_from_dna_list(dna_list, **metadata)
        else:
            success_count = await seeder.seed_from_dna_list(dna_list)

        result = {
            "success_count": success_count,
            "total_count": len(dna_list),
            "success_rate": success_count / len(dna_list) if dna_list else 0.0,
            "message": f"成功填充 {success_count}/{len(dna_list)} 个情感基因"
        }

        log_info("情感基因API", "批量填充情感基因完成",
                成功数量=success_count, 总数量=len(dna_list))

        return result

    except Exception as e:
        log_error("情感基因API", "批量填充情感基因失败", error=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量填充情感基因失败: {str(e)}"
        )
