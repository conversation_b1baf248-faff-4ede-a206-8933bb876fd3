"""
💾 [数据库] 章节仓库测试
测试章节数据仓库的CRUD操作和业务逻辑
"""

import pytest
import tempfile
from pathlib import Path
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker

from app.core.database import Base
from app.repositories.bible_repo import StoryBibleRepository
from app.repositories.chapter_repo import ChapterRepository
from app.schemas.generation import StoryGenre, AIProvider, GenerationStatus
from app.core.config import log_info, log_debug


class TestChapterRepository:
    """💾 [数据库] 章节仓库测试类"""
    
    @pytest.fixture
    async def test_session_with_bible(self):
        """创建包含故事圣经的测试数据库会话"""
        # 创建临时数据库
        temp_dir = tempfile.mkdtemp()
        temp_db_path = Path(temp_dir) / "test_chapter_repo.db"
        db_url = f"sqlite+aiosqlite:///{temp_db_path}"
        
        log_debug("数据库", "创建章节仓库测试数据库", 路径=str(temp_db_path))
        
        # 创建引擎和会话
        engine = create_async_engine(db_url)
        async_session_maker = async_sessionmaker(bind=engine, class_=AsyncSession)
        
        # 创建表结构
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        # 创建测试故事圣经
        async with async_session_maker() as session:
            bible_repo = StoryBibleRepository(session)
            bible_data = {
                "id": "test-bible-for-chapters",
                "title": "章节测试小说",
                "genre": StoryGenre.FANTASY,
                "theme": "测试主题",
                "protagonist": "测试主角",
                "setting": "测试背景",
                "plot_outline": "测试大纲",
                "ai_provider": AIProvider.ZHIPU,
                "temperature": 0.8,
                "max_tokens": 3000,
                "status": GenerationStatus.PENDING
            }
            await bible_repo.create_bible(bible_data)
        
        # 提供会话
        async with async_session_maker() as session:
            yield session, "test-bible-for-chapters"
        
        # 清理
        await engine.dispose()
        try:
            if temp_db_path.exists():
                temp_db_path.unlink()
        except PermissionError:
            pass
    
    @pytest.fixture
    def sample_chapter_data(self):
        """示例章节数据"""
        return {
            "id": "test-chapter-001",
            "story_bible_id": "test-bible-for-chapters",
            "chapter_number": 1,
            "chapter_title": "第一章：魔法的觉醒",
            "chapter_outline": "主角发现自己的魔法天赋",
            "previous_chapter_summary": None,
            "character_development": "主角性格内向转为自信",
            "plot_requirements": "需要展现魔法世界的神奇",
            "target_word_count": 2000,
            "ai_provider": AIProvider.ZHIPU,
            "temperature": 0.8,
            "max_tokens": 4000,
            "status": GenerationStatus.PENDING
        }
    
    async def test_create_chapter(self, test_session_with_bible, sample_chapter_data):
        """💾 [数据库] 测试创建章节"""
        session, bible_id = test_session_with_bible
        repo = ChapterRepository(session)
        
        # 创建章节
        chapter = await repo.create_chapter(sample_chapter_data)
        
        # 验证创建结果
        assert chapter.id == sample_chapter_data["id"]
        assert chapter.story_bible_id == bible_id
        assert chapter.chapter_number == 1
        assert chapter.chapter_title == sample_chapter_data["chapter_title"]
        assert chapter.status == GenerationStatus.PENDING
        assert chapter.created_at is not None
        
        log_info("数据库", "章节创建测试通过", 章节ID=chapter.id)
    
    async def test_get_chapter_by_id(self, test_session_with_bible, sample_chapter_data):
        """💾 [数据库] 测试根据ID获取章节"""
        session, bible_id = test_session_with_bible
        repo = ChapterRepository(session)
        
        # 先创建章节
        created_chapter = await repo.create_chapter(sample_chapter_data)
        
        # 根据ID获取
        retrieved_chapter = await repo.get_chapter_by_id(created_chapter.id)
        
        # 验证获取结果
        assert retrieved_chapter is not None
        assert retrieved_chapter.id == created_chapter.id
        assert retrieved_chapter.chapter_title == created_chapter.chapter_title
        
        # 测试不存在的ID
        non_existent = await repo.get_chapter_by_id("non-existent-id")
        assert non_existent is None
        
        log_info("数据库", "章节查询测试通过")
    
    async def test_get_chapters_by_bible_id(self, test_session_with_bible):
        """💾 [数据库] 测试获取故事圣经的所有章节"""
        session, bible_id = test_session_with_bible
        repo = ChapterRepository(session)
        
        # 创建多个章节
        chapter_data_list = [
            {
                "id": f"test-chapter-{i:03d}",
                "story_bible_id": bible_id,
                "chapter_number": i,
                "chapter_title": f"第{i}章",
                "chapter_outline": f"第{i}章大纲",
                "target_word_count": 2000,
                "ai_provider": AIProvider.ZHIPU,
                "temperature": 0.8,
                "max_tokens": 4000,
                "status": GenerationStatus.PENDING
            }
            for i in range(1, 4)
        ]
        
        for chapter_data in chapter_data_list:
            await repo.create_chapter(chapter_data)
        
        # 获取所有章节
        chapters = await repo.get_chapters_by_bible_id(bible_id)
        
        # 验证结果
        assert len(chapters) == 3
        assert chapters[0].chapter_number == 1
        assert chapters[1].chapter_number == 2
        assert chapters[2].chapter_number == 3
        
        # 测试不存在的故事圣经ID
        empty_chapters = await repo.get_chapters_by_bible_id("non-existent-bible")
        assert len(empty_chapters) == 0
        
        log_info("数据库", "故事圣经章节查询测试通过")
    
    async def test_update_chapter_status(self, test_session_with_bible, sample_chapter_data):
        """💾 [数据库] 测试更新章节状态"""
        session, bible_id = test_session_with_bible
        repo = ChapterRepository(session)
        
        # 创建章节
        chapter = await repo.create_chapter(sample_chapter_data)
        
        # 更新状态为生成中
        success = await repo.update_chapter_status(
            chapter.id, 
            GenerationStatus.GENERATING
        )
        assert success is True
        
        # 验证状态更新 - 重新查询以避免懒加载问题
        await session.refresh(chapter)
        assert chapter.status == GenerationStatus.GENERATING
        
        # 更新为完成状态，包含生成内容
        generated_content = "这是生成的章节内容，包含了精彩的故事情节..."
        success = await repo.update_chapter_status(
            chapter.id,
            GenerationStatus.COMPLETED,
            generated_content=generated_content,
            generation_time=8.5,
            token_usage=2500,
            actual_word_count=1850
        )
        assert success is True
        
        # 验证完成状态更新 - 重新查询以避免懒加载问题
        await session.refresh(chapter)
        assert chapter.status == GenerationStatus.COMPLETED
        assert chapter.generated_content == generated_content
        assert chapter.content_length == len(generated_content)
        assert chapter.generation_time == 8.5
        assert chapter.token_usage == 2500
        assert chapter.actual_word_count == 1850
        assert chapter.completed_at is not None
        
        # 测试更新不存在的记录
        success = await repo.update_chapter_status(
            "non-existent-id", 
            GenerationStatus.FAILED
        )
        assert success is False
        
        log_info("数据库", "章节状态更新测试通过")
    
    async def test_delete_chapter(self, test_session_with_bible, sample_chapter_data):
        """💾 [数据库] 测试删除章节"""
        session, bible_id = test_session_with_bible
        repo = ChapterRepository(session)
        
        # 创建章节
        chapter = await repo.create_chapter(sample_chapter_data)
        
        # 验证存在
        retrieved = await repo.get_chapter_by_id(chapter.id)
        assert retrieved is not None
        
        # 删除章节
        success = await repo.delete_chapter(chapter.id)
        assert success is True
        
        # 验证已删除
        deleted = await repo.get_chapter_by_id(chapter.id)
        assert deleted is None
        
        # 测试删除不存在的记录
        success = await repo.delete_chapter("non-existent-id")
        assert success is False
        
        log_info("数据库", "章节删除测试通过")
    
    async def test_get_chapter_count_by_bible_id(self, test_session_with_bible):
        """💾 [数据库] 测试获取故事圣经的章节数量"""
        session, bible_id = test_session_with_bible
        repo = ChapterRepository(session)
        
        # 初始状态应该没有章节
        count = await repo.get_chapter_count_by_bible_id(bible_id)
        assert count == 0
        
        # 创建几个章节
        for i in range(1, 4):
            chapter_data = {
                "id": f"test-chapter-{i:03d}",
                "story_bible_id": bible_id,
                "chapter_number": i,
                "chapter_title": f"第{i}章",
                "chapter_outline": f"第{i}章大纲",
                "target_word_count": 2000,
                "ai_provider": AIProvider.ZHIPU,
                "temperature": 0.8,
                "max_tokens": 4000,
                "status": GenerationStatus.PENDING
            }
            await repo.create_chapter(chapter_data)
        
        # 验证章节数量
        count = await repo.get_chapter_count_by_bible_id(bible_id)
        assert count == 3
        
        # 测试不存在的故事圣经ID
        count = await repo.get_chapter_count_by_bible_id("non-existent-bible")
        assert count == 0
        
        log_info("数据库", "章节数量查询测试通过")
    
    async def test_get_next_chapter_number(self, test_session_with_bible):
        """💾 [数据库] 测试获取下一个章节号"""
        session, bible_id = test_session_with_bible
        repo = ChapterRepository(session)
        
        # 初始状态应该返回1
        next_number = await repo.get_next_chapter_number(bible_id)
        assert next_number == 1
        
        # 创建几个章节
        for i in range(1, 4):
            chapter_data = {
                "id": f"test-chapter-{i:03d}",
                "story_bible_id": bible_id,
                "chapter_number": i,
                "chapter_title": f"第{i}章",
                "chapter_outline": f"第{i}章大纲",
                "target_word_count": 2000,
                "ai_provider": AIProvider.ZHIPU,
                "temperature": 0.8,
                "max_tokens": 4000,
                "status": GenerationStatus.PENDING
            }
            await repo.create_chapter(chapter_data)
        
        # 验证下一个章节号
        next_number = await repo.get_next_chapter_number(bible_id)
        assert next_number == 4
        
        # 测试不存在的故事圣经ID
        next_number = await repo.get_next_chapter_number("non-existent-bible")
        assert next_number == 1
        
        log_info("数据库", "下一个章节号查询测试通过")
