"""
🎯 [情感映射] 情感物理映射器模块

将情感标签映射为具体的生理反应和感官触发器，
从情感基因库中抽取真实的人类情感细节，
并将这些细节注入到AI生成的提示词中。

作者: 文心小说后端服务系统
创建时间: 2025-08-02
"""

import re
import random
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

from app.core.config import log_info, log_debug, log_error
from app.repositories.emotional_gene_repo import EmotionalGeneRepository
from app.models.emotional_gene import EmotionalGene


@dataclass
class EmotionMapping:
    """情感映射结果数据类"""
    emotion_tag: str
    intensity: float
    physiological_reactions: List[str]
    sensory_triggers: List[str]
    entropy_items: List[str]
    source_genes: List[int]  # 来源基因ID列表


@dataclass
class EmotionEnhancedPrompt:
    """情感增强后的提示词数据类"""
    original_prompt: str
    enhanced_prompt: str
    emotion_mappings: List[EmotionMapping]
    enhancement_instructions: List[str]


class EmotionRecognizer:
    """🧠 情感识别器 - 从文本中识别情感标签和强度"""
    
    def __init__(self):
        # 情感关键词映射表
        self.emotion_keywords = {
            "愤怒": ["愤怒", "生气", "暴怒", "恼火", "愤慨", "怒火", "暴躁"],
            "恐惧": ["恐惧", "害怕", "惊恐", "恐慌", "畏惧", "胆怯", "惊吓"],
            "悲伤": ["悲伤", "难过", "伤心", "痛苦", "哀伤", "忧郁", "沮丧"],
            "喜悦": ["喜悦", "高兴", "快乐", "兴奋", "愉快", "欢乐", "狂欢"],
            "惊讶": ["惊讶", "震惊", "诧异", "惊奇", "意外", "吃惊", "惊愕"],
            "厌恶": ["厌恶", "恶心", "反感", "憎恶", "嫌弃", "讨厌", "排斥"],
            "羞耻": ["羞耻", "羞愧", "尴尬", "难堪", "羞辱", "耻辱", "窘迫"],
            "嫉妒": ["嫉妒", "妒忌", "羡慕", "眼红", "不甘", "怨恨", "醋意"],
            "背叛": ["背叛", "出卖", "欺骗", "辜负", "违背", "叛变", "不忠"],
            "绝望": ["绝望", "无望", "失望", "崩溃", "绝境", "无助", "破灭"]
        }
        
        # 强度修饰词
        self.intensity_modifiers = {
            "极度": 1.0, "非常": 0.9, "十分": 0.8, "很": 0.7, "相当": 0.6,
            "有些": 0.5, "略微": 0.4, "稍微": 0.3, "一点": 0.2, "微微": 0.1
        }
        
        log_debug("情感映射", "情感识别器初始化完成", 情感类型数=len(self.emotion_keywords))
    
    def recognize_emotions(self, text: str) -> List[Tuple[str, float]]:
        """
        🔍 从文本中识别情感标签和强度
        
        Args:
            text: 输入文本
            
        Returns:
            List[Tuple[str, float]]: 情感标签和强度的列表
        """
        log_debug("情感映射", "开始识别情感", 文本长度=len(text))
        
        emotions = []
        text_lower = text.lower()
        
        for emotion_tag, keywords in self.emotion_keywords.items():
            for keyword in keywords:
                if keyword in text_lower:
                    # 查找强度修饰词
                    intensity = self._calculate_intensity(text_lower, keyword)
                    emotions.append((emotion_tag, intensity))
                    log_debug("情感映射", "识别到情感", 情感=emotion_tag, 关键词=keyword, 强度=intensity)
                    break  # 找到一个关键词就跳出
        
        # 去重并合并相同情感的强度
        emotion_dict = {}
        for emotion, intensity in emotions:
            if emotion in emotion_dict:
                emotion_dict[emotion] = max(emotion_dict[emotion], intensity)
            else:
                emotion_dict[emotion] = intensity
        
        result = list(emotion_dict.items())
        log_info("情感映射", "情感识别完成", 识别到的情感数=len(result))
        return result
    
    def _calculate_intensity(self, text: str, keyword: str) -> float:
        """计算情感强度"""
        # 在关键词前后查找强度修饰词
        keyword_pos = text.find(keyword)
        if keyword_pos == -1:
            return 0.5  # 默认中等强度
        
        # 检查前面的修饰词
        before_text = text[:keyword_pos]
        for modifier, intensity in self.intensity_modifiers.items():
            if modifier in before_text[-10:]:  # 只检查前10个字符
                return intensity
        
        return 0.5  # 默认中等强度


class EmotionPhysicalMapper:
    """🧬 情感物理映射器 - 核心映射引擎"""
    
    def __init__(self, gene_repository: EmotionalGeneRepository):
        self.gene_repository = gene_repository
        self.emotion_recognizer = EmotionRecognizer()
        log_debug("情感映射", "情感物理映射器初始化完成")
    
    async def extract_emotion_details(
        self,
        emotion_tag: str,
        intensity: float = 0.5,
        count: int = 3,
        min_quality: float = 0.6
    ) -> EmotionMapping:
        """
        🔍 从情感基因库中抽取情感细节
        
        Args:
            emotion_tag: 情感标签
            intensity: 情感强度 (0.0-1.0)
            count: 抽取的基因数量
            min_quality: 最低质量要求
            
        Returns:
            EmotionMapping: 情感映射结果
        """
        log_debug("情感映射", "开始抽取情感细节", 
                 情感标签=emotion_tag, 强度=intensity, 数量=count)
        
        try:
            # 从基因库搜索相关基因
            genes, total = await self.gene_repository.search_genes(
                emotion_tag=emotion_tag,
                min_quality=min_quality,
                min_intensity=intensity * 0.8,  # 允许稍低的强度
                limit=count * 2,  # 多获取一些以便筛选
                order_by="quality_score",
                order_direction="desc"
            )
            
            if not genes:
                log_debug("情感映射", "未找到匹配的情感基因，使用随机基因", 情感标签=emotion_tag)
                # 如果没有找到精确匹配，尝试获取随机高质量基因
                genes = await self.gene_repository.get_random_genes(
                    count=count,
                    min_quality=min_quality
                )
            
            # 选择最佳基因
            selected_genes = genes[:count]
            
            # 合并所有基因的细节
            physiological_reactions = []
            sensory_triggers = []
            entropy_items = []
            source_genes = []
            
            for gene in selected_genes:
                if gene.physiological_reactions:
                    physiological_reactions.extend(gene.physiological_reactions)
                if gene.sensory_triggers:
                    sensory_triggers.extend(gene.sensory_triggers)
                if gene.entropy_items:
                    entropy_items.extend(gene.entropy_items)
                source_genes.append(gene.id)
                
                # 增加基因使用次数
                await self.gene_repository.increment_usage(gene.id)
            
            # 去重并随机选择
            physiological_reactions = list(set(physiological_reactions))
            sensory_triggers = list(set(sensory_triggers))
            entropy_items = list(set(entropy_items))
            
            # 根据强度调整数量
            max_reactions = max(1, int(intensity * 5))
            max_triggers = max(1, int(intensity * 4))
            max_entropy = max(1, int(intensity * 3))
            
            physiological_reactions = random.sample(
                physiological_reactions, 
                min(len(physiological_reactions), max_reactions)
            )
            sensory_triggers = random.sample(
                sensory_triggers,
                min(len(sensory_triggers), max_triggers)
            )
            entropy_items = random.sample(
                entropy_items,
                min(len(entropy_items), max_entropy)
            )
            
            mapping = EmotionMapping(
                emotion_tag=emotion_tag,
                intensity=intensity,
                physiological_reactions=physiological_reactions,
                sensory_triggers=sensory_triggers,
                entropy_items=entropy_items,
                source_genes=source_genes
            )
            
            log_info("情感映射", "情感细节抽取完成",
                    情感标签=emotion_tag,
                    生理反应数=len(physiological_reactions),
                    感官触发器数=len(sensory_triggers),
                    熵增项目数=len(entropy_items))
            
            return mapping
            
        except Exception as e:
            log_error("情感映射", "抽取情感细节失败", error=e, 情感标签=emotion_tag)
            # 返回空的映射结果
            return EmotionMapping(
                emotion_tag=emotion_tag,
                intensity=intensity,
                physiological_reactions=[],
                sensory_triggers=[],
                entropy_items=[],
                source_genes=[]
            )
    
    async def auto_recognize_and_extract(
        self,
        text: str,
        max_emotions: int = 3
    ) -> List[EmotionMapping]:
        """
        🤖 自动识别文本中的情感并抽取细节
        
        Args:
            text: 输入文本
            max_emotions: 最大情感数量
            
        Returns:
            List[EmotionMapping]: 情感映射结果列表
        """
        log_debug("情感映射", "开始自动识别和抽取情感", 文本长度=len(text))
        
        # 识别情感
        emotions = self.emotion_recognizer.recognize_emotions(text)
        
        # 按强度排序，取前几个
        emotions.sort(key=lambda x: x[1], reverse=True)
        emotions = emotions[:max_emotions]
        
        # 为每个情感抽取细节
        mappings = []
        for emotion_tag, intensity in emotions:
            mapping = await self.extract_emotion_details(emotion_tag, intensity)
            mappings.append(mapping)
        
        log_info("情感映射", "自动识别和抽取完成", 处理的情感数=len(mappings))
        return mappings

    async def enhance_prompt_with_emotions(
        self,
        original_prompt: str,
        specified_emotions: Optional[List[str]] = None,
        emotion_intensity: float = 0.7,
        auto_detect: bool = True
    ) -> EmotionEnhancedPrompt:
        """
        ✨ 使用情感细节增强提示词

        Args:
            original_prompt: 原始提示词
            specified_emotions: 指定的情感标签列表
            emotion_intensity: 情感强度
            auto_detect: 是否自动检测情感

        Returns:
            EmotionEnhancedPrompt: 增强后的提示词
        """
        log_debug("情感映射", "开始增强提示词",
                 原始提示词长度=len(original_prompt),
                 指定情感=specified_emotions,
                 自动检测=auto_detect)

        mappings = []

        # 处理指定的情感
        if specified_emotions:
            for emotion_tag in specified_emotions:
                mapping = await self.extract_emotion_details(
                    emotion_tag=emotion_tag,
                    intensity=emotion_intensity
                )
                mappings.append(mapping)

        # 自动检测情感
        if auto_detect:
            auto_mappings = await self.auto_recognize_and_extract(original_prompt)
            mappings.extend(auto_mappings)

        # 去重（相同情感标签只保留强度最高的）
        emotion_dict = {}
        for mapping in mappings:
            if mapping.emotion_tag not in emotion_dict or mapping.intensity > emotion_dict[mapping.emotion_tag].intensity:
                emotion_dict[mapping.emotion_tag] = mapping

        final_mappings = list(emotion_dict.values())

        # 生成增强指令
        enhancement_instructions = self._generate_enhancement_instructions(final_mappings)

        # 构建增强后的提示词
        enhanced_prompt = self._build_enhanced_prompt(original_prompt, enhancement_instructions)

        result = EmotionEnhancedPrompt(
            original_prompt=original_prompt,
            enhanced_prompt=enhanced_prompt,
            emotion_mappings=final_mappings,
            enhancement_instructions=enhancement_instructions
        )

        log_info("情感映射", "提示词增强完成",
                原始长度=len(original_prompt),
                增强后长度=len(enhanced_prompt),
                情感数量=len(final_mappings))

        return result

    def _generate_enhancement_instructions(self, mappings: List[EmotionMapping]) -> List[str]:
        """生成情感增强指令"""
        instructions = []

        if not mappings:
            return instructions

        instructions.append("【情感细节强制要求】")
        instructions.append("在生成内容时，必须包含以下真实的人类情感细节：")
        instructions.append("")

        for i, mapping in enumerate(mappings, 1):
            instructions.append(f"{i}. 情感：{mapping.emotion_tag} (强度: {mapping.intensity:.1f})")

            if mapping.physiological_reactions:
                instructions.append("   生理反应：")
                for reaction in mapping.physiological_reactions:
                    instructions.append(f"   - {reaction}")

            if mapping.sensory_triggers:
                instructions.append("   感官细节：")
                for trigger in mapping.sensory_triggers:
                    instructions.append(f"   - {trigger}")

            if mapping.entropy_items:
                instructions.append("   环境细节：")
                for item in mapping.entropy_items:
                    instructions.append(f"   - {item}")

            instructions.append("")

        instructions.append("【重要提醒】")
        instructions.append("- 这些细节必须自然地融入到情节中，不能生硬堆砌")
        instructions.append("- 优先使用生理反应来表现情感，而不是直接描述情感")
        instructions.append("- 感官细节要与场景环境相符")
        instructions.append("- 避免过度完美的描写，保持真实感")

        return instructions

    def _build_enhanced_prompt(self, original_prompt: str, instructions: List[str]) -> str:
        """构建增强后的提示词"""
        if not instructions:
            return original_prompt

        instruction_text = "\n".join(instructions)

        enhanced_prompt = f"""{original_prompt}

{instruction_text}

请严格按照以上情感细节要求进行创作，确保生成的内容具有真实的人类情感体验。"""

        return enhanced_prompt


# 便捷函数
async def create_emotion_mapper(gene_repository: EmotionalGeneRepository) -> EmotionPhysicalMapper:
    """创建情感物理映射器实例"""
    return EmotionPhysicalMapper(gene_repository)


async def quick_enhance_prompt(
    prompt: str,
    gene_repository: EmotionalGeneRepository,
    emotions: Optional[List[str]] = None,
    intensity: float = 0.7
) -> str:
    """
    快速增强提示词的便捷函数

    Args:
        prompt: 原始提示词
        gene_repository: 情感基因仓库
        emotions: 指定情感列表
        intensity: 情感强度

    Returns:
        str: 增强后的提示词
    """
    mapper = EmotionPhysicalMapper(gene_repository)
    result = await mapper.enhance_prompt_with_emotions(
        original_prompt=prompt,
        specified_emotions=emotions,
        emotion_intensity=intensity
    )
    return result.enhanced_prompt
