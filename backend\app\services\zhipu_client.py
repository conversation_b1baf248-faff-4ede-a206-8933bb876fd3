"""
🌐 [API] 智谱AI客户端服务
提供与智谱AI API交互的异步客户端服务，支持对话补全、流式输出和错误重试
"""

from typing import Dict, List, Optional, Any, AsyncGenerator
import httpx
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
import json
from pydantic import BaseModel

from app.core.config import settings, log_info, log_error, log_debug, log_warning


class ChatMessage(BaseModel):
    """聊天消息模型"""
    role: str  # user, assistant, system
    content: str


class ChatCompletionRequest(BaseModel):
    """智谱AI聊天补全请求模型"""
    model: str = settings.zhipu_model
    messages: List[ChatMessage]
    temperature: float = settings.default_temperature
    max_tokens: int = settings.default_max_tokens
    stream: bool = False
    top_p: float = 0.7
    do_sample: bool = True


class ChatCompletionResponse(BaseModel):
    """智谱AI聊天补全响应模型"""
    id: str
    object: str
    created: int
    model: str
    choices: List[Dict[str, Any]]
    usage: Dict[str, int]


class ZhipuAIError(Exception):
    """智谱AI API错误异常"""
    def __init__(self, message: str, status_code: Optional[int] = None, error_type: Optional[str] = None):
        self.message = message
        self.status_code = status_code
        self.error_type = error_type
        super().__init__(self.message)


class ZhipuClient:
    """
    🌐 [API] 智谱AI异步客户端
    
    提供与智谱AI API的异步交互功能，包括：
    - 聊天补全
    - 流式输出
    - 自动重试
    - 错误处理
    """
    
    def __init__(self):
        self.api_key = settings.zhipu_api_key
        self.base_url = settings.zhipu_base_url.rstrip('/')
        self.model = settings.zhipu_model
        self.client = httpx.AsyncClient(timeout=60.0)
        
        log_info("API", "智谱AI客户端初始化完成", 
                模型=self.model,
                基础URL=self.base_url)
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
    
    async def close(self):
        """关闭客户端连接"""
        if self.client:
            await self.client.aclose()
            log_debug("API", "智谱AI客户端连接已关闭")
    
    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        return {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "User-Agent": "wenxin-novel-backend/0.1.0"
        }
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((httpx.TimeoutException, httpx.ConnectError))
    )
    async def chat_completion(
        self, 
        messages: List[ChatMessage],
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        stream: bool = False
    ) -> ChatCompletionResponse:
        """
        🌐 [API] 执行聊天补全请求
        
        Args:
            messages: 聊天消息列表
            model: 模型名称，默认使用配置中的模型
            temperature: 温度参数，控制随机性
            max_tokens: 最大输出token数
            stream: 是否使用流式输出
            
        Returns:
            ChatCompletionResponse: 聊天补全响应
            
        Raises:
            ZhipuAIError: API调用错误
        """
        request_data = ChatCompletionRequest(
            model=model or self.model,
            messages=messages,
            temperature=temperature or settings.default_temperature,
            max_tokens=max_tokens or settings.default_max_tokens,
            stream=stream
        )
        
        log_info("API", "开始调用智谱AI聊天补全", 
                模型=request_data.model,
                消息数量=len(messages),
                温度=request_data.temperature,
                最大Token=request_data.max_tokens,
                流式输出=stream)
        
        try:
            url = f"{self.base_url}/chat/completions"
            headers = self._get_headers()
            
            log_debug("API", "发送请求到智谱AI", 
                     URL=url,
                     请求数据=request_data.model_dump())
            
            response = await self.client.post(
                url=url,
                headers=headers,
                json=request_data.model_dump(),
                timeout=60.0
            )
            
            # 检查HTTP状态码
            if response.status_code != 200:
                error_msg = f"智谱AI API调用失败"
                error_type = "http_error"
                try:
                    error_data = await response.json()
                    error_detail = error_data.get("error", {})
                    error_msg = f"{error_msg}: {error_detail.get('message', '未知错误')}"
                    error_type = error_detail.get("type", "http_error")
                except:
                    error_msg = f"{error_msg}: HTTP {response.status_code}"
                
                log_error("API", error_msg, 
                         状态码=response.status_code,
                         响应内容=response.text[:500])
                
                raise ZhipuAIError(
                    message=error_msg,
                    status_code=response.status_code,
                    error_type=error_type
                )
            
            # 解析响应
            response_data = await response.json()
            completion_response = ChatCompletionResponse(**response_data)
            
            log_info("API", "智谱AI API调用成功", 
                    响应ID=completion_response.id,
                    输入Token=completion_response.usage.get("prompt_tokens", 0),
                    输出Token=completion_response.usage.get("completion_tokens", 0),
                    总Token=completion_response.usage.get("total_tokens", 0))
            
            return completion_response
            
        except httpx.TimeoutException as e:
            error_msg = "智谱AI API调用超时"
            log_error("API", error_msg, error=e)
            raise ZhipuAIError(message=error_msg, error_type="timeout")
            
        except httpx.ConnectError as e:
            error_msg = "无法连接到智谱AI API"
            log_error("API", error_msg, error=e)
            raise ZhipuAIError(message=error_msg, error_type="connection")
        
        except ZhipuAIError:
            # 重新抛出ZhipuAIError，不要修改
            raise
            
        except Exception as e:
            error_msg = f"智谱AI API调用发生未知错误: {str(e)}"
            log_error("API", error_msg, error=e)
            raise ZhipuAIError(message=error_msg, error_type="unknown")
    
    async def chat_completion_stream(
        self,
        messages: List[ChatMessage],
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None
    ) -> AsyncGenerator[str, None]:
        """
        🌐 [API] 流式聊天补全
        
        Args:
            messages: 聊天消息列表
            model: 模型名称
            temperature: 温度参数
            max_tokens: 最大输出token数
            
        Yields:
            str: 流式输出的文本片段
            
        Raises:
            ZhipuAIError: API调用错误
        """
        request_data = ChatCompletionRequest(
            model=model or self.model,
            messages=messages,
            temperature=temperature or settings.default_temperature,
            max_tokens=max_tokens or settings.default_max_tokens,
            stream=True
        )
        
        log_info("API", "开始智谱AI流式对话", 
                模型=request_data.model,
                消息数量=len(messages))
        
        try:
            url = f"{self.base_url}/chat/completions"
            headers = self._get_headers()
            
            async with self.client.stream(
                "POST",
                url=url,
                headers=headers,
                json=request_data.model_dump(),
                timeout=settings.stream_timeout
            ) as response:
                
                if response.status_code != 200:
                    error_msg = f"智谱AI流式API调用失败: HTTP {response.status_code}"
                    log_error("API", error_msg, 状态码=response.status_code)
                    raise ZhipuAIError(
                        message=error_msg,
                        status_code=response.status_code,
                        error_type="http_error"
                    )
                
                buffer = ""
                async for chunk in response.aiter_bytes(chunk_size=settings.stream_chunk_size):
                    buffer += chunk.decode('utf-8')
                    
                    # 处理SSE格式的数据
                    lines = buffer.split('\n')
                    buffer = lines[-1]  # 保留最后一个不完整的行
                    
                    for line in lines[:-1]:
                        line = line.strip()
                        if line.startswith('data: '):
                            data_str = line[6:]  # 移除 'data: ' 前缀
                            
                            if data_str == '[DONE]':
                                log_info("API", "智谱AI流式对话结束")
                                return
                            
                            try:
                                data = json.loads(data_str)
                                choices = data.get('choices', [])
                                if choices and len(choices) > 0:
                                    delta = choices[0].get('delta', {})
                                    content = delta.get('content', '')
                                    if content:
                                        yield content
                                        
                            except json.JSONDecodeError:
                                log_warning("API", "解析流式响应JSON失败", 数据=data_str[:100])
                                continue
                
        except httpx.TimeoutException as e:
            error_msg = "智谱AI流式API调用超时"
            log_error("API", error_msg, error=e)
            raise ZhipuAIError(message=error_msg, error_type="timeout")
            
        except Exception as e:
            error_msg = f"智谱AI流式API调用失败: {str(e)}"
            log_error("API", error_msg, error=e)
            raise ZhipuAIError(message=error_msg, error_type="unknown")


# 全局智谱AI客户端实例
_zhipu_client: Optional[ZhipuClient] = None


async def get_zhipu_client() -> ZhipuClient:
    """
    🌐 [API] 获取智谱AI客户端实例（单例模式）
    """
    global _zhipu_client
    if _zhipu_client is None:
        _zhipu_client = ZhipuClient()
        log_info("API", "创建智谱AI客户端单例实例")
    return _zhipu_client


async def close_zhipu_client():
    """
    🌐 [API] 关闭智谱AI客户端实例
    """
    global _zhipu_client
    if _zhipu_client:
        await _zhipu_client.close()
        _zhipu_client = None
        log_info("API", "智谱AI客户端实例已关闭")