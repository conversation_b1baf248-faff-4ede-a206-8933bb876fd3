"""
💾 [数据库] 故事圣经数据仓库
提供故事圣经的数据访问功能
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, func, and_, or_
from sqlalchemy.orm import selectinload

from app.models.story_bible import StoryBible
from app.schemas.generation import GenerationStatus, AIProvider, StoryGenre
from app.core.config import log_info, log_error, log_debug


class StoryBibleRepository:
    """
    💾 [数据库] 故事圣经数据仓库
    
    提供故事圣经的CRUD操作和业务查询功能
    """
    
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def create_bible(self, bible_data: Dict[str, Any]) -> StoryBible:
        """
        💾 [数据库] 创建新的故事圣经
        
        Args:
            bible_data: 故事圣经数据字典
            
        Returns:
            StoryBible: 创建的故事圣经对象
        """
        log_debug("数据库", "开始创建故事圣经", 数据=bible_data)
        
        try:
            bible = StoryBible(**bible_data)
            self.session.add(bible)
            await self.session.commit()
            await self.session.refresh(bible)
            
            log_info("数据库", "故事圣经创建成功", 
                    故事圣经ID=bible.id, 
                    标题=bible.title,
                    状态=bible.status.value)
            
            return bible
            
        except Exception as e:
            await self.session.rollback()
            log_error("数据库", "故事圣经创建失败", error=e, 数据=bible_data)
            raise
    
    async def get_bible_by_id(self, bible_id: str) -> Optional[StoryBible]:
        """
        💾 [数据库] 根据ID获取故事圣经
        
        Args:
            bible_id: 故事圣经ID
            
        Returns:
            Optional[StoryBible]: 故事圣经对象，如果不存在则返回None
        """
        log_debug("数据库", "查询故事圣经", 故事圣经ID=bible_id)
        
        try:
            stmt = select(StoryBible).where(StoryBible.id == bible_id)
            result = await self.session.execute(stmt)
            bible = result.scalar_one_or_none()
            
            if bible:
                log_debug("数据库", "故事圣经查询成功", 
                         故事圣经ID=bible.id, 
                         标题=bible.title)
            else:
                log_debug("数据库", "故事圣经不存在", 故事圣经ID=bible_id)
            
            return bible
            
        except Exception as e:
            log_error("数据库", "故事圣经查询失败", error=e, 故事圣经ID=bible_id)
            raise
    
    async def get_bible_with_chapters(self, bible_id: str) -> Optional[StoryBible]:
        """
        💾 [数据库] 获取故事圣经及其所有章节
        
        Args:
            bible_id: 故事圣经ID
            
        Returns:
            Optional[StoryBible]: 包含章节的故事圣经对象
        """
        log_debug("数据库", "查询故事圣经及章节", 故事圣经ID=bible_id)
        
        try:
            stmt = (
                select(StoryBible)
                .options(selectinload(StoryBible.chapters))
                .where(StoryBible.id == bible_id)
            )
            result = await self.session.execute(stmt)
            bible = result.scalar_one_or_none()
            
            if bible:
                log_debug("数据库", "故事圣经及章节查询成功", 
                         故事圣经ID=bible.id, 
                         章节数量=len(bible.chapters))
            else:
                log_debug("数据库", "故事圣经不存在", 故事圣经ID=bible_id)
            
            return bible
            
        except Exception as e:
            log_error("数据库", "故事圣经及章节查询失败", error=e, 故事圣经ID=bible_id)
            raise
    
    async def update_bible_status(
        self, 
        bible_id: str, 
        status: GenerationStatus,
        generated_content: Optional[str] = None,
        error_message: Optional[str] = None,
        generation_time: Optional[float] = None,
        token_usage: Optional[int] = None
    ) -> bool:
        """
        💾 [数据库] 更新故事圣经状态
        
        Args:
            bible_id: 故事圣经ID
            status: 新状态
            generated_content: 生成的内容
            error_message: 错误信息
            generation_time: 生成耗时
            token_usage: token使用量
            
        Returns:
            bool: 更新是否成功
        """
        log_debug("数据库", "更新故事圣经状态", 
                 故事圣经ID=bible_id, 
                 新状态=status.value)
        
        try:
            update_data = {
                "status": status,
                "updated_at": datetime.utcnow()
            }
            
            if generated_content is not None:
                update_data["generated_content"] = generated_content
                update_data["content_length"] = len(generated_content)
            
            if error_message is not None:
                update_data["error_message"] = error_message
            
            if generation_time is not None:
                update_data["generation_time"] = generation_time
            
            if token_usage is not None:
                update_data["token_usage"] = token_usage
            
            if status == GenerationStatus.COMPLETED:
                update_data["completed_at"] = datetime.utcnow()
            
            stmt = (
                update(StoryBible)
                .where(StoryBible.id == bible_id)
                .values(**update_data)
            )
            
            result = await self.session.execute(stmt)
            await self.session.commit()

            success = result.rowcount > 0
            
            if success:
                log_info("数据库", "故事圣经状态更新成功", 
                        故事圣经ID=bible_id, 
                        新状态=status.value)
            else:
                log_error("数据库", "故事圣经状态更新失败 - 记录不存在", 
                         故事圣经ID=bible_id)
            
            return success
            
        except Exception as e:
            await self.session.rollback()
            log_error("数据库", "故事圣经状态更新失败", 
                     error=e, 
                     故事圣经ID=bible_id, 
                     新状态=status.value)
            raise
    
    async def list_bibles(
        self, 
        limit: int = 50, 
        offset: int = 0,
        status_filter: Optional[GenerationStatus] = None,
        genre_filter: Optional[StoryGenre] = None
    ) -> List[StoryBible]:
        """
        💾 [数据库] 获取故事圣经列表
        
        Args:
            limit: 限制数量
            offset: 偏移量
            status_filter: 状态过滤
            genre_filter: 类型过滤
            
        Returns:
            List[StoryBible]: 故事圣经列表
        """
        log_debug("数据库", "查询故事圣经列表", 
                 限制=limit, 
                 偏移=offset, 
                 状态过滤=status_filter.value if status_filter else None,
                 类型过滤=genre_filter.value if genre_filter else None)
        
        try:
            stmt = select(StoryBible)
            
            # 添加过滤条件
            if status_filter:
                stmt = stmt.where(StoryBible.status == status_filter)
            
            if genre_filter:
                stmt = stmt.where(StoryBible.genre == genre_filter)
            
            # 排序和分页
            stmt = (
                stmt
                .order_by(StoryBible.created_at.desc())
                .limit(limit)
                .offset(offset)
            )
            
            result = await self.session.execute(stmt)
            bibles = result.scalars().all()
            
            log_debug("数据库", "故事圣经列表查询成功", 
                     返回数量=len(bibles))
            
            return list(bibles)
            
        except Exception as e:
            log_error("数据库", "故事圣经列表查询失败", error=e)
            raise
    
    async def delete_bible(self, bible_id: str) -> bool:
        """
        💾 [数据库] 删除故事圣经
        
        Args:
            bible_id: 故事圣经ID
            
        Returns:
            bool: 删除是否成功
        """
        log_debug("数据库", "删除故事圣经", 故事圣经ID=bible_id)
        
        try:
            stmt = delete(StoryBible).where(StoryBible.id == bible_id)
            result = await self.session.execute(stmt)
            await self.session.commit()
            
            success = result.rowcount > 0
            
            if success:
                log_info("数据库", "故事圣经删除成功", 故事圣经ID=bible_id)
            else:
                log_error("数据库", "故事圣经删除失败 - 记录不存在", 故事圣经ID=bible_id)
            
            return success
            
        except Exception as e:
            await self.session.rollback()
            log_error("数据库", "故事圣经删除失败", error=e, 故事圣经ID=bible_id)
            raise



