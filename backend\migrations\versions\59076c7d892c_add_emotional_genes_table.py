"""Add emotional_genes table

Revision ID: 59076c7d892c
Revises: f491e78ccb49
Create Date: 2025-08-02 14:36:45.793175

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite

# revision identifiers, used by Alembic.
revision: str = '59076c7d892c'
down_revision: Union[str, Sequence[str], None] = 'f491e78ccb49'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('emotional_genes',
    sa.Column('id', sa.Integer(), nullable=False, comment='情感基因唯一标识'),
    sa.Column('emotion_tag', sa.String(length=100), nullable=False, comment='情感标签'),
    sa.Column('source_text', sa.Text(), nullable=False, comment='原始文本'),
    sa.Column('physiological_reactions', sqlite.JSON(), nullable=False, comment='生理反应列表'),
    sa.Column('sensory_triggers', sqlite.JSON(), nullable=False, comment='感官触发器列表'),
    sa.Column('entropy_items', sqlite.JSON(), nullable=False, comment='熵增项目列表'),
    sa.Column('intensity_score', sa.Float(), nullable=False, comment='强度评分 (0.0-1.0)'),
    sa.Column('reliability_score', sa.Float(), nullable=False, comment='可靠性评分 (0.0-1.0)'),
    sa.Column('quality_score', sa.Float(), nullable=False, comment='综合质量评分 (0.0-1.0)'),
    sa.Column('purification_level', sa.String(length=50), nullable=False, comment='提纯等级'),
    sa.Column('purification_method', sa.String(length=100), nullable=True, comment='提纯方法'),
    sa.Column('source_type', sa.String(length=50), nullable=True, comment='来源类型 (文学作品/真实故事等)'),
    sa.Column('source_author', sa.String(length=200), nullable=True, comment='来源作者'),
    sa.Column('source_title', sa.String(length=500), nullable=True, comment='来源标题'),
    sa.Column('category', sa.String(length=100), nullable=True, comment='情感类别'),
    sa.Column('subcategory', sa.String(length=100), nullable=True, comment='情感子类别'),
    sa.Column('context_tags', sqlite.JSON(), nullable=True, comment='上下文标签列表'),
    sa.Column('usage_count', sa.Integer(), nullable=False, comment='使用次数'),
    sa.Column('last_used_at', sa.DateTime(), nullable=True, comment='最后使用时间'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_category_intensity', 'emotional_genes', ['category', 'intensity_score'], unique=False)
    op.create_index('idx_emotion_tag_quality', 'emotional_genes', ['emotion_tag', 'quality_score'], unique=False)
    op.create_index('idx_purification_level', 'emotional_genes', ['purification_level'], unique=False)
    op.create_index('idx_reliability_created', 'emotional_genes', ['reliability_score', 'created_at'], unique=False)
    op.create_index('idx_source_type', 'emotional_genes', ['source_type'], unique=False)
    op.create_index('idx_usage_stats', 'emotional_genes', ['usage_count', 'last_used_at'], unique=False)
    op.create_index(op.f('ix_emotional_genes_category'), 'emotional_genes', ['category'], unique=False)
    op.create_index(op.f('ix_emotional_genes_emotion_tag'), 'emotional_genes', ['emotion_tag'], unique=False)
    op.create_index(op.f('ix_emotional_genes_id'), 'emotional_genes', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_emotional_genes_id'), table_name='emotional_genes')
    op.drop_index(op.f('ix_emotional_genes_emotion_tag'), table_name='emotional_genes')
    op.drop_index(op.f('ix_emotional_genes_category'), table_name='emotional_genes')
    op.drop_index('idx_usage_stats', table_name='emotional_genes')
    op.drop_index('idx_source_type', table_name='emotional_genes')
    op.drop_index('idx_reliability_created', table_name='emotional_genes')
    op.drop_index('idx_purification_level', table_name='emotional_genes')
    op.drop_index('idx_emotion_tag_quality', table_name='emotional_genes')
    op.drop_index('idx_category_intensity', table_name='emotional_genes')
    op.drop_table('emotional_genes')
    # ### end Alembic commands ###
