"""
📝 [生成] 小说生成相关的数据模型
定义AI小说生成服务的请求和响应数据结构
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from enum import Enum


class AIProvider(str, Enum):
    """AI服务提供商枚举"""
    ZHIPU = "zhipu"
    KIMI = "kimi"


class GenerationStatus(str, Enum):
    """生成状态枚举"""
    PENDING = "pending"     # 等待中
    GENERATING = "generating"  # 生成中
    COMPLETED = "completed"    # 已完成
    FAILED = "failed"         # 失败


class StoryGenre(str, Enum):
    """小说类型枚举"""
    ROMANCE = "romance"           # 言情
    FANTASY = "fantasy"           # 奇幻
    URBAN = "urban"              # 都市
    HISTORICAL = "historical"     # 历史
    MYSTERY = "mystery"          # 悬疑
    SCIFI = "scifi"              # 科幻
    MARTIAL_ARTS = "martial_arts" # 武侠
    THRILLER = "thriller"         # 惊悚


class StoryBibleRequest(BaseModel):
    """故事圣经生成请求模型"""
    title: str = Field(..., description="小说标题", min_length=1, max_length=100)
    genre: StoryGenre = Field(..., description="小说类型")
    theme: str = Field(..., description="小说主题", min_length=10, max_length=500)
    protagonist: str = Field(..., description="主角设定", min_length=10, max_length=300)
    setting: str = Field(..., description="故事背景", min_length=10, max_length=500)
    plot_outline: str = Field(..., description="情节大纲", min_length=20, max_length=1000)
    target_audience: Optional[str] = Field(None, description="目标读者群体", max_length=100)
    writing_style: Optional[str] = Field(None, description="写作风格要求", max_length=200)
    
    # AI生成参数
    ai_provider: AIProvider = Field(default=AIProvider.ZHIPU, description="AI服务提供商")
    temperature: float = Field(default=0.8, ge=0.1, le=1.0, description="创作随机性")
    max_tokens: int = Field(default=3000, gt=0, le=8000, description="最大生成长度")


class ChapterGenerationRequest(BaseModel):
    """章节生成请求模型"""
    story_bible_id: str = Field(..., description="故事圣经ID")
    chapter_number: int = Field(..., ge=1, description="章节号")
    chapter_title: str = Field(..., description="章节标题", min_length=1, max_length=100)
    chapter_outline: str = Field(..., description="章节大纲", min_length=10, max_length=500)
    previous_chapter_summary: Optional[str] = Field(None, description="前一章节摘要", max_length=300)
    character_development: Optional[str] = Field(None, description="角色发展要求", max_length=300)
    plot_requirements: Optional[str] = Field(None, description="情节要求", max_length=300)

    # 情感增强流水线参数
    enable_emotion_enhancement: bool = Field(default=True, description="启用情感增强")
    specified_emotions: Optional[List[str]] = Field(None, description="指定情感标签列表")
    emotion_intensity: float = Field(default=0.7, ge=0.1, le=1.0, description="情感强度")
    auto_detect_emotions: bool = Field(default=True, description="自动检测情感")
    enable_entropy_injection: bool = Field(default=True, description="启用熵增扰动")
    disruption_level: str = Field(default="medium", description="扰动级别: low/medium/high")
    custom_disruption_intensity: Optional[float] = Field(None, ge=0.0, le=1.0, description="自定义扰动强度")

    # AI生成参数
    ai_provider: AIProvider = Field(default=AIProvider.ZHIPU, description="AI服务提供商")
    temperature: float = Field(default=0.8, ge=0.1, le=1.0, description="创作随机性")
    max_tokens: int = Field(default=4000, gt=0, le=10000, description="最大生成长度")
    target_word_count: int = Field(default=2000, gt=0, le=5000, description="目标字数")


class GenerationTaskBase(BaseModel):
    """生成任务基础模型"""
    id: str = Field(..., description="任务ID")
    task_type: str = Field(..., description="任务类型") # story_bible, chapter
    status: GenerationStatus = Field(default=GenerationStatus.PENDING, description="生成状态")
    ai_provider: AIProvider = Field(..., description="AI服务提供商")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")
    error_message: Optional[str] = Field(None, description="错误信息")


class StoryBibleResponse(GenerationTaskBase):
    """故事圣经响应模型"""
    task_type: str = Field(default="story_bible", description="任务类型")
    
    # 输入数据
    request_data: StoryBibleRequest = Field(..., description="请求数据")
    
    # 生成结果
    generated_content: Optional[str] = Field(None, description="生成的故事圣经内容")
    character_profiles: Optional[List[Dict[str, Any]]] = Field(None, description="角色档案")
    world_building: Optional[str] = Field(None, description="世界观设定")
    plot_structure: Optional[str] = Field(None, description="情节结构")
    writing_guidelines: Optional[str] = Field(None, description="写作指南")
    
    # 生成统计
    generation_stats: Optional[Dict[str, Any]] = Field(None, description="生成统计信息")


class ChapterResponse(GenerationTaskBase):
    """章节生成响应模型"""
    task_type: str = Field(default="chapter", description="任务类型")
    
    # 输入数据
    request_data: ChapterGenerationRequest = Field(..., description="请求数据")
    
    # 生成结果
    generated_content: Optional[str] = Field(None, description="生成的章节内容")
    chapter_summary: Optional[str] = Field(None, description="章节摘要")
    character_development_notes: Optional[str] = Field(None, description="角色发展记录")
    plot_advancement: Optional[str] = Field(None, description="情节推进记录")
    
    # 生成统计
    generation_stats: Optional[Dict[str, Any]] = Field(None, description="生成统计信息")


class GenerationProgress(BaseModel):
    """生成进度模型"""
    task_id: str = Field(..., description="任务ID")
    progress: float = Field(..., ge=0.0, le=1.0, description="进度百分比")
    current_stage: str = Field(..., description="当前阶段")
    estimated_remaining_time: Optional[int] = Field(None, description="预计剩余时间(秒)")
    partial_content: Optional[str] = Field(None, description="部分生成内容")


class GenerationError(BaseModel):
    """生成错误模型"""
    error_code: str = Field(..., description="错误代码")
    error_message: str = Field(..., description="错误信息")
    error_type: str = Field(..., description="错误类型")
    timestamp: str = Field(..., description="错误时间")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")


class TaskStatusResponse(BaseModel):
    """任务状态查询响应"""
    task_id: str = Field(..., description="任务ID")
    status: GenerationStatus = Field(..., description="任务状态")
    progress: Optional[GenerationProgress] = Field(None, description="进度信息")
    result: Optional[Dict[str, Any]] = Field(None, description="结果数据")
    error: Optional[GenerationError] = Field(None, description="错误信息")


class GenerationListResponse(BaseModel):
    """生成任务列表响应"""
    total: int = Field(..., description="总数量")
    tasks: List[GenerationTaskBase] = Field(..., description="任务列表")
    page: int = Field(default=1, description="页码")
    page_size: int = Field(default=20, description="每页大小")


class HealthCheckResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(..., description="服务状态")
    message: str = Field(..., description="状态消息")
    version: str = Field(..., description="版本号")
    service: str = Field(..., description="服务名称")
    ai_providers: Dict[str, Any] = Field(..., description="AI服务提供商信息")
    timestamp: Optional[str] = Field(None, description="时间戳")