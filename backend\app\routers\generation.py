"""
📝 [生成] 小说生成API路由
提供故事圣经生成、章节生成等核心业务API接口
"""

from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends, Query
from fastapi.responses import JSONResponse

from app.schemas.generation import (
    StoryBibleRequest, StoryBibleResponse,
    ChapterGenerationRequest, ChapterResponse,
    GenerationStatus
)
from app.core.config import log_info, log_error, log_debug, log_success
from app.core.database import get_bible_repository, get_chapter_repository
from app.repositories.bible_repo import StoryBibleRepository
from app.repositories.chapter_repo import ChapterRepository
from app.models.story_bible import StoryGenre, AIProvider
from app.services.generation_service import (
    create_task_id, create_bible_data, create_chapter_data
)
from app.services.background_tasks import (
    background_generate_story_bible, background_generate_chapter,
    format_bible_response, format_chapter_response
)


# 创建路由器
router = APIRouter(prefix="/api/v1", tags=["小说生成"])


@router.post("/generate-bible", response_model=StoryBibleResponse, summary="生成故事圣经")
async def generate_story_bible(
    request: StoryBibleRequest,
    background_tasks: BackgroundTasks,
    bible_repo: StoryBibleRepository = Depends(get_bible_repository)
) -> StoryBibleResponse:
    """
    📝 [生成] 生成故事圣经
    
    根据用户提供的小说基础信息，生成详细的故事圣经，包括角色档案、世界观设定、情节结构等。
    
    Args:
        request: 故事圣经生成请求
        background_tasks: 后台任务管理器
        bible_repo: 故事圣经仓库
        
    Returns:
        StoryBibleResponse: 包含任务ID和初始状态的响应
    """
    try:
        # 生成任务ID
        task_id = create_task_id()
        
        log_info("生成", "收到故事圣经生成请求", 
                任务ID=task_id,
                标题=request.title,
                类型=request.genre.value,
                AI提供商=request.ai_provider.value)
        
        # 创建数据库记录
        bible_data = create_bible_data(task_id, request)
        bible = await bible_repo.create_bible(bible_data)
        
        # 添加后台生成任务
        background_tasks.add_task(background_generate_story_bible, task_id, request, bible_repo)
        
        # 构造响应
        response = StoryBibleResponse(
            id=task_id,
            task_type="story_bible",
            status=GenerationStatus.PENDING,
            ai_provider=request.ai_provider,
            created_at=bible.created_at.isoformat(),
            updated_at=bible.updated_at.isoformat(),
            request_data=request
        )
        
        log_success("生成", "故事圣经生成任务已创建", 任务ID=task_id)
        return response
        
    except Exception as e:
        log_error("生成", "创建故事圣经生成任务失败", error=e)
        raise HTTPException(status_code=500, detail=f"创建生成任务失败: {str(e)}")


@router.post("/generate-chapter", response_model=ChapterResponse, summary="生成章节")
async def generate_chapter(
    request: ChapterGenerationRequest,
    background_tasks: BackgroundTasks,
    chapter_repo: ChapterRepository = Depends(get_chapter_repository),
    bible_repo: StoryBibleRepository = Depends(get_bible_repository)
) -> ChapterResponse:
    """
    📝 [生成] 生成章节内容
    
    根据故事圣经和章节要求，生成具体的章节内容。
    
    Args:
        request: 章节生成请求
        background_tasks: 后台任务管理器
        chapter_repo: 章节仓库
        bible_repo: 故事圣经仓库
        
    Returns:
        ChapterResponse: 包含任务ID和初始状态的响应
    """
    try:
        # 生成任务ID
        task_id = create_task_id()
        
        log_info("生成", "收到章节生成请求",
                任务ID=task_id,
                故事圣经ID=request.story_bible_id,
                章节号=request.chapter_number,
                章节标题=request.chapter_title)
        
        # 验证故事圣经存在
        story_bible = await bible_repo.get_bible_by_id(request.story_bible_id)
        if not story_bible:
            raise HTTPException(status_code=404, detail=f"未找到故事圣经: {request.story_bible_id}")
        
        # 创建数据库记录
        chapter_data = create_chapter_data(task_id, request)
        chapter = await chapter_repo.create_chapter(chapter_data)
        
        # 添加后台生成任务
        background_tasks.add_task(background_generate_chapter, task_id, request, chapter_repo)
        
        # 构造响应
        response = ChapterResponse(
            id=task_id,
            task_type="chapter",
            status=GenerationStatus.PENDING,
            ai_provider=request.ai_provider,
            created_at=chapter.created_at.isoformat(),
            updated_at=chapter.updated_at.isoformat(),
            request_data=request
        )
        
        log_success("生成", "章节生成任务已创建", 任务ID=task_id)
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        log_error("生成", "创建章节生成任务失败", error=e)
        raise HTTPException(status_code=500, detail=f"创建生成任务失败: {str(e)}")


@router.get("/bible/{bible_id}", summary="获取故事圣经")
async def get_story_bible(
    bible_id: str,
    bible_repo: StoryBibleRepository = Depends(get_bible_repository)
) -> Dict[str, Any]:
    """
    📁 [文件] 获取故事圣经详情
    
    根据ID获取故事圣经的详细信息和生成状态。
    
    Args:
        bible_id: 故事圣经ID
        bible_repo: 故事圣经仓库
        
    Returns:
        Dict: 故事圣经详情
    """
    try:
        log_debug("生成", "查询故事圣经详情", 故事圣经ID=bible_id)

        bible = await bible_repo.get_bible_by_id(bible_id)
        if not bible:
            raise HTTPException(status_code=404, detail=f"未找到故事圣经: {bible_id}")

        return format_bible_response(bible)

    except HTTPException:
        raise
    except Exception as e:
        log_error("生成", "查询故事圣经详情失败", error=e, 故事圣经ID=bible_id)
        raise HTTPException(status_code=500, detail=f"查询故事圣经详情失败: {str(e)}")


@router.get("/chapter/{chapter_id}", summary="获取章节")
async def get_chapter(
    chapter_id: str,
    chapter_repo: ChapterRepository = Depends(get_chapter_repository)
) -> Dict[str, Any]:
    """
    📁 [文件] 获取章节详情

    根据ID获取章节的详细信息和生成状态。

    Args:
        chapter_id: 章节ID
        chapter_repo: 章节仓库

    Returns:
        Dict: 章节详情
    """
    try:
        log_debug("生成", "查询章节详情", 章节ID=chapter_id)

        chapter = await chapter_repo.get_chapter_by_id(chapter_id)
        if not chapter:
            raise HTTPException(status_code=404, detail=f"未找到章节: {chapter_id}")

        return format_chapter_response(chapter)

    except HTTPException:
        raise
    except Exception as e:
        log_error("生成", "查询章节详情失败", error=e, 章节ID=chapter_id)
        raise HTTPException(status_code=500, detail=f"查询章节详情失败: {str(e)}")


@router.get("/bibles", summary="获取故事圣经列表")
async def list_story_bibles(
    limit: int = Query(20, ge=1, le=100, description="每页数量"),
    offset: int = Query(0, ge=0, description="偏移量"),
    status: Optional[GenerationStatus] = Query(None, description="状态筛选"),
    genre: Optional[StoryGenre] = Query(None, description="类型筛选"),
    bible_repo: StoryBibleRepository = Depends(get_bible_repository)
) -> Dict[str, Any]:
    """
    📁 [文件] 获取故事圣经列表
    
    分页获取故事圣经列表，支持按状态和类型筛选。
    
    Args:
        limit: 每页数量
        offset: 偏移量
        status: 状态筛选
        genre: 类型筛选
        bible_repo: 故事圣经仓库
        
    Returns:
        Dict: 分页的故事圣经列表
    """
    try:
        log_debug("生成", "查询故事圣经列表", 
                 限制=limit, 偏移=offset, 状态筛选=status, 类型筛选=genre)
        
        # 查询数据
        bibles = await bible_repo.list_bibles(
            limit=limit,
            offset=offset,
            status_filter=status,
            genre_filter=genre
        )

        # 计算总数（简化版本，实际应该有单独的count方法）
        total = len(bibles)
        
        # 格式化响应
        items = [format_bible_response(bible) for bible in bibles]
        
        return {
            "total": total,
            "items": items,
            "limit": limit,
            "offset": offset
        }
        
    except Exception as e:
        log_error("生成", "查询故事圣经列表失败", error=e)
        raise HTTPException(status_code=500, detail=f"查询故事圣经列表失败: {str(e)}")


@router.get("/bible/{bible_id}/chapters", summary="获取故事圣经的章节列表")
async def list_bible_chapters(
    bible_id: str,
    limit: int = Query(20, ge=1, le=100, description="每页数量"),
    offset: int = Query(0, ge=0, description="偏移量"),
    status: Optional[GenerationStatus] = Query(None, description="状态筛选"),
    chapter_repo: ChapterRepository = Depends(get_chapter_repository),
    bible_repo: StoryBibleRepository = Depends(get_bible_repository)
) -> Dict[str, Any]:
    """
    📁 [文件] 获取指定故事圣经的章节列表

    Args:
        bible_id: 故事圣经ID
        limit: 每页数量
        offset: 偏移量
        status: 状态筛选
        chapter_repo: 章节仓库
        bible_repo: 故事圣经仓库

    Returns:
        Dict: 分页的章节列表
    """
    try:
        log_debug("生成", "查询故事圣经章节列表",
                 故事圣经ID=bible_id, 限制=limit, 偏移=offset, 状态筛选=status)

        # 验证故事圣经存在
        bible = await bible_repo.get_bible_by_id(bible_id)
        if not bible:
            raise HTTPException(status_code=404, detail=f"未找到故事圣经: {bible_id}")

        # 查询数据
        all_chapters = await chapter_repo.get_chapters_by_bible_id(bible_id)

        # 手动筛选状态（如果需要）
        if status:
            all_chapters = [ch for ch in all_chapters if ch.status == status]

        # 手动分页
        total = len(all_chapters)
        chapters = all_chapters[offset:offset + limit]

        # 格式化响应
        items = [format_chapter_response(chapter) for chapter in chapters]

        return {
            "total": total,
            "items": items,
            "limit": limit,
            "offset": offset,
            "bible_id": bible_id
        }

    except HTTPException:
        raise
    except Exception as e:
        log_error("生成", "查询故事圣经章节列表失败", error=e, 故事圣经ID=bible_id)
        raise HTTPException(status_code=500, detail=f"查询章节列表失败: {str(e)}")


@router.delete("/bible/{bible_id}", summary="删除故事圣经")
async def delete_story_bible(
    bible_id: str,
    bible_repo: StoryBibleRepository = Depends(get_bible_repository)
) -> Dict[str, str]:
    """
    🗑️ [删除] 删除故事圣经

    删除指定的故事圣经及其所有关联的章节。

    Args:
        bible_id: 故事圣经ID
        bible_repo: 故事圣经仓库

    Returns:
        Dict: 删除结果消息
    """
    try:
        log_debug("生成", "删除故事圣经", 故事圣经ID=bible_id)

        # 验证故事圣经存在
        bible = await bible_repo.get_bible_by_id(bible_id)
        if not bible:
            raise HTTPException(status_code=404, detail=f"未找到故事圣经: {bible_id}")

        # 删除故事圣经（级联删除章节）
        await bible_repo.delete_bible(bible_id)

        log_success("生成", "故事圣经删除成功", 故事圣经ID=bible_id)
        return {"message": f"故事圣经 {bible_id} 已成功删除"}

    except HTTPException:
        raise
    except Exception as e:
        log_error("生成", "删除故事圣经失败", error=e, 故事圣经ID=bible_id)
        raise HTTPException(status_code=500, detail=f"删除故事圣经失败: {str(e)}")


@router.delete("/chapter/{chapter_id}", summary="删除章节")
async def delete_chapter(
    chapter_id: str,
    chapter_repo: ChapterRepository = Depends(get_chapter_repository)
) -> Dict[str, str]:
    """
    🗑️ [删除] 删除章节

    删除指定的章节。

    Args:
        chapter_id: 章节ID
        chapter_repo: 章节仓库

    Returns:
        Dict: 删除结果消息
    """
    try:
        log_debug("生成", "删除章节", 章节ID=chapter_id)

        # 验证章节存在
        chapter = await chapter_repo.get_chapter_by_id(chapter_id)
        if not chapter:
            raise HTTPException(status_code=404, detail=f"未找到章节: {chapter_id}")

        # 删除章节
        await chapter_repo.delete_chapter(chapter_id)

        log_success("生成", "章节删除成功", 章节ID=chapter_id)
        return {"message": f"章节 {chapter_id} 已成功删除"}

    except HTTPException:
        raise
    except Exception as e:
        log_error("生成", "删除章节失败", error=e, 章节ID=chapter_id)
        raise HTTPException(status_code=500, detail=f"删除章节失败: {str(e)}")
