<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">90%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-02 12:57 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698___init___py.html">app\__init__.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2___init___py.html">app\core\__init__.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t68">app\core\config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t68"><data value='allowed_origins_list'>Settings.allowed_origins_list</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t87">app\core\config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t87"><data value='setup_logging'>setup_logging</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t109">app\core\config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t109"><data value='log_info'>log_info</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t115">app\core\config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t115"><data value='log_error'>log_error</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t126">app\core\config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t126"><data value='log_debug'>log_debug</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t132">app\core\config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t132"><data value='log_warning'>log_warning</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t138">app\core\config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t138"><data value='log_success'>log_success</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html">app\core\config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>36</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="36 36">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d___init___py.html">app\models\__init__.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_84e85958b078cc7a___init___py.html">app\routers\__init__.py</a></td>
                <td class="name left"><a href="z_84e85958b078cc7a___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_84e85958b078cc7a_generation_py.html#t29">app\routers\generation.py</a></td>
                <td class="name left"><a href="z_84e85958b078cc7a_generation_py.html#t29"><data value='create_task_id'>_create_task_id</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_84e85958b078cc7a_generation_py.html#t34">app\routers\generation.py</a></td>
                <td class="name left"><a href="z_84e85958b078cc7a_generation_py.html#t34"><data value='get_current_timestamp'>_get_current_timestamp</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_84e85958b078cc7a_generation_py.html#t39">app\routers\generation.py</a></td>
                <td class="name left"><a href="z_84e85958b078cc7a_generation_py.html#t39"><data value='store_task'>_store_task</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_84e85958b078cc7a_generation_py.html#t45">app\routers\generation.py</a></td>
                <td class="name left"><a href="z_84e85958b078cc7a_generation_py.html#t45"><data value='get_task'>_get_task</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_84e85958b078cc7a_generation_py.html#t50">app\routers\generation.py</a></td>
                <td class="name left"><a href="z_84e85958b078cc7a_generation_py.html#t50"><data value='update_task_status'>_update_task_status</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_84e85958b078cc7a_generation_py.html#t60">app\routers\generation.py</a></td>
                <td class="name left"><a href="z_84e85958b078cc7a_generation_py.html#t60"><data value='generate_story_bible_content'>_generate_story_bible_content</data></a></td>
                <td>7</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="4 7">57%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_84e85958b078cc7a_generation_py.html#t125">app\routers\generation.py</a></td>
                <td class="name left"><a href="z_84e85958b078cc7a_generation_py.html#t125"><data value='generate_chapter_content'>_generate_chapter_content</data></a></td>
                <td>7</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="4 7">57%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_84e85958b078cc7a_generation_py.html#t171">app\routers\generation.py</a></td>
                <td class="name left"><a href="z_84e85958b078cc7a_generation_py.html#t171"><data value='background_generate_story_bible'>_background_generate_story_bible</data></a></td>
                <td>9</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="7 9">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_84e85958b078cc7a_generation_py.html#t204">app\routers\generation.py</a></td>
                <td class="name left"><a href="z_84e85958b078cc7a_generation_py.html#t204"><data value='background_generate_chapter'>_background_generate_chapter</data></a></td>
                <td>15</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="11 15">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_84e85958b078cc7a_generation_py.html#t254">app\routers\generation.py</a></td>
                <td class="name left"><a href="z_84e85958b078cc7a_generation_py.html#t254"><data value='generate_story_bible'>generate_story_bible</data></a></td>
                <td>13</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="10 13">77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_84e85958b078cc7a_generation_py.html#t320">app\routers\generation.py</a></td>
                <td class="name left"><a href="z_84e85958b078cc7a_generation_py.html#t320"><data value='generate_chapter'>generate_chapter</data></a></td>
                <td>18</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="15 18">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_84e85958b078cc7a_generation_py.html#t393">app\routers\generation.py</a></td>
                <td class="name left"><a href="z_84e85958b078cc7a_generation_py.html#t393"><data value='get_task_status'>get_task_status</data></a></td>
                <td>12</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="9 12">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_84e85958b078cc7a_generation_py.html#t435">app\routers\generation.py</a></td>
                <td class="name left"><a href="z_84e85958b078cc7a_generation_py.html#t435"><data value='get_task_list'>get_task_list</data></a></td>
                <td>14</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="11 14">79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_84e85958b078cc7a_generation_py.html">app\routers\generation.py</a></td>
                <td class="name left"><a href="z_84e85958b078cc7a_generation_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>27</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="27 27">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174___init___py.html">app\schemas\__init__.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html">app\schemas\generation.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>99</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="99 99">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70___init___py.html">app\services\__init__.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_zhipu_client_py.html#t44">app\services\zhipu_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_zhipu_client_py.html#t44"><data value='init__'>ZhipuAIError.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_zhipu_client_py.html#t62">app\services\zhipu_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_zhipu_client_py.html#t62"><data value='init__'>ZhipuClient.__init__</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_zhipu_client_py.html#t72">app\services\zhipu_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_zhipu_client_py.html#t72"><data value='aenter__'>ZhipuClient.__aenter__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_zhipu_client_py.html#t76">app\services\zhipu_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_zhipu_client_py.html#t76"><data value='aexit__'>ZhipuClient.__aexit__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_zhipu_client_py.html#t80">app\services\zhipu_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_zhipu_client_py.html#t80"><data value='close'>ZhipuClient.close</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_zhipu_client_py.html#t86">app\services\zhipu_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_zhipu_client_py.html#t86"><data value='get_headers'>ZhipuClient._get_headers</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_zhipu_client_py.html#t99">app\services\zhipu_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_zhipu_client_py.html#t99"><data value='chat_completion'>ZhipuClient.chat_completion</data></a></td>
                <td>37</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="35 37">95%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_zhipu_client_py.html#t206">app\services\zhipu_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_zhipu_client_py.html#t206"><data value='chat_completion_stream'>ZhipuClient.chat_completion_stream</data></a></td>
                <td>41</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="27 41">66%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_zhipu_client_py.html#t306">app\services\zhipu_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_zhipu_client_py.html#t306"><data value='get_zhipu_client'>get_zhipu_client</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_zhipu_client_py.html#t317">app\services\zhipu_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_zhipu_client_py.html#t317"><data value='close_zhipu_client'>close_zhipu_client</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_zhipu_client_py.html">app\services\zhipu_client.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_zhipu_client_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>38</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="38 38">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1___init___py.html">app\utils\__init__.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>419</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="376 419">90%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-02 12:57 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
