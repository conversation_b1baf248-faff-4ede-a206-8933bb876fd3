"""
🧬 情感DNA提纯器测试模块

测试情感DNA编译器的各个组件功能：
1. 脱壳器测试
2. 脱水器测试  
3. 基因配对器测试
4. 熵增生成器测试
5. DNA编译器集成测试

作者: 文心小说后端服务系统
创建时间: 2025-08-02
"""

import pytest
from unittest.mock import Mock, patch
from typing import Dict, List

from app.core.dna_compiler import (
    DNACompiler, ShellRemover, Dehydrator, GenePairer, 
    EntropyGenerator, EmotionalDNA, PurificationLevel
)
from app.core.config import log_debug, log_info, log_error


class TestShellRemover:
    """🐚 [脱壳器测试] 测试脱壳器功能"""
    
    def test_init_shell_remover(self):
        """测试脱壳器初始化"""
        log_debug("测试", "开始测试脱壳器初始化")
        
        remover = ShellRemover()
        
        # 验证初始化
        assert hasattr(remover, 'metaphor_indicators')
        assert hasattr(remover, 'physical_keywords')
        assert len(remover.metaphor_indicators) > 0
        assert len(remover.physical_keywords) > 0
        assert "像" in remover.metaphor_indicators
        assert "心跳" in remover.physical_keywords
        
        log_info("测试", "脱壳器初始化测试通过")
    
    def test_remove_metaphors(self):
        """测试移除比喻句"""
        log_debug("测试", "开始测试移除比喻句")
        
        remover = ShellRemover()
        
        # 测试文本包含比喻句和物理反应
        text = "他的心跳像打鼓一样剧烈。她感到胸口发闷。他的脸色像纸一样苍白。她的手在颤抖。"
        
        result = remover.remove_metaphors(text)
        
        # 验证结果
        assert "她感到胸口发闷" in result
        assert "她的手在颤抖" in result
        # 比喻句应该被过滤掉
        assert "像打鼓一样" not in result or "心跳" in result  # 如果包含物理反应可能保留
        
        log_info("测试", "移除比喻句测试通过")
    
    def test_extract_physical_reactions(self):
        """测试提取物理反应"""
        log_debug("测试", "开始测试提取物理反应")
        
        remover = ShellRemover()
        
        text = "他感到心跳加速。她的呼吸变得急促。天空很美丽。他的手掌出汗了。"
        
        reactions = remover.extract_physical_reactions(text)
        
        # 验证结果
        assert len(reactions) >= 2  # 至少提取到2个物理反应
        assert any("心跳" in reaction for reaction in reactions)
        assert any("呼吸" in reaction for reaction in reactions)
        assert not any("天空很美丽" in reaction for reaction in reactions)
        
        log_info("测试", "提取物理反应测试通过", 提取数量=len(reactions))


class TestDehydrator:
    """💧 [脱水器测试] 测试脱水器功能"""
    
    def test_init_dehydrator(self):
        """测试脱水器初始化"""
        log_debug("测试", "开始测试脱水器初始化")
        
        dehydrator = Dehydrator()
        
        # 验证初始化
        assert hasattr(dehydrator, 'lyrical_words')
        assert hasattr(dehydrator, 'sensory_keywords')
        assert len(dehydrator.lyrical_words) > 0
        assert len(dehydrator.sensory_keywords) > 0
        assert "美丽" in dehydrator.lyrical_words
        assert "视觉" in dehydrator.sensory_keywords
        
        log_info("测试", "脱水器初始化测试通过")
    
    def test_remove_lyrical_words(self):
        """测试移除抒情词汇"""
        log_debug("测试", "开始测试移除抒情词汇")
        
        dehydrator = Dehydrator()
        
        text = "这是一个美丽的场景，让人感到震撼和激动。他看到了明亮的光线。"
        
        result = dehydrator.remove_lyrical_words(text)
        
        # 验证结果
        assert "美丽" not in result
        assert "震撼" not in result
        assert "激动" not in result
        assert "明亮" in result  # 感官词汇应该保留
        assert "光线" in result
        
        log_info("测试", "移除抒情词汇测试通过")
    
    def test_extract_sensory_data(self):
        """测试提取感官数据"""
        log_debug("测试", "开始测试提取感官数据")
        
        dehydrator = Dehydrator()
        
        text = "他看到了明亮的光线。她听到了美妙的音乐。空气中闻到了花香。他感觉到了温暖。"
        
        sensory_data = dehydrator.extract_sensory_data(text)
        
        # 验证结果
        assert isinstance(sensory_data, dict)
        assert len(sensory_data) >= 3  # 至少3种感官类型
        
        # 检查具体感官数据
        if "视觉" in sensory_data:
            assert len(sensory_data["视觉"]) > 0
        if "听觉" in sensory_data:
            assert len(sensory_data["听觉"]) > 0
        if "嗅觉" in sensory_data:
            assert len(sensory_data["嗅觉"]) > 0
        
        log_info("测试", "提取感官数据测试通过", 感官类型数=len(sensory_data))


class TestGenePairer:
    """🧬 [基因配对器测试] 测试基因配对器功能"""
    
    def test_init_gene_pairer(self):
        """测试基因配对器初始化"""
        log_debug("测试", "开始测试基因配对器初始化")
        
        pairer = GenePairer()
        
        # 验证初始化
        assert hasattr(pairer, 'intensity_keywords')
        assert len(pairer.intensity_keywords) > 0
        assert "高强度" in pairer.intensity_keywords
        assert "剧烈" in pairer.intensity_keywords["高强度"]
        
        log_info("测试", "基因配对器初始化测试通过")
    
    def test_calculate_intensity_score(self):
        """测试计算情感强度评分"""
        log_debug("测试", "开始测试计算情感强度评分")
        
        pairer = GenePairer()
        
        # 测试高强度文本
        high_intensity_text = "他感到剧烈的心跳和强烈的恐惧。"
        high_score = pairer.calculate_intensity_score(high_intensity_text)
        
        # 测试低强度文本
        low_intensity_text = "他稍微感到一点紧张。"
        low_score = pairer.calculate_intensity_score(low_intensity_text)
        
        # 验证结果
        assert 0.0 <= high_score <= 1.0
        assert 0.0 <= low_score <= 1.0
        assert high_score > low_score  # 高强度应该得分更高
        
        log_info("测试", "计算情感强度评分测试通过", 高强度评分=high_score, 低强度评分=low_score)
    
    def test_calculate_reliability_score(self):
        """测试计算可靠性评分"""
        log_debug("测试", "开始测试计算可靠性评分")
        
        pairer = GenePairer()
        
        # 测试数据
        physical_reactions = ["心跳加速", "呼吸急促", "手掌出汗"]
        sensory_data = {
            "视觉": ["看到明亮光线"],
            "听觉": ["听到声音"],
            "触觉": ["感到温暖"]
        }
        
        score = pairer.calculate_reliability_score(physical_reactions, sensory_data)
        
        # 验证结果
        assert 0.0 <= score <= 1.0
        assert score > 0.0  # 有数据应该得分大于0
        
        log_info("测试", "计算可靠性评分测试通过", 评分=score)
    
    def test_pair_genes(self):
        """测试基因配对"""
        log_debug("测试", "开始测试基因配对")
        
        pairer = GenePairer()
        
        emotion_tag = "恐惧"
        physical_reactions = ["心跳加速", "呼吸急促"]
        sensory_data = {"视觉": ["看到阴影"], "听觉": ["听到脚步声"]}
        source_text = "他感到强烈的恐惧，心跳加速，呼吸急促。"
        
        dna = pairer.pair_genes(emotion_tag, physical_reactions, sensory_data, source_text)
        
        # 验证结果
        assert isinstance(dna, EmotionalDNA)
        assert dna.emotion_tag == emotion_tag
        assert dna.physiological_reactions == physical_reactions
        assert len(dna.sensory_triggers) > 0
        assert 0.0 <= dna.intensity_score <= 1.0
        assert 0.0 <= dna.reliability_score <= 1.0
        assert dna.source_text == source_text
        
        log_info("测试", "基因配对测试通过", 情感标签=dna.emotion_tag)


class TestEntropyGenerator:
    """🌀 [熵增生成器测试] 测试熵增生成器功能"""
    
    def test_init_entropy_generator(self):
        """测试熵增生成器初始化"""
        log_debug("测试", "开始测试熵增生成器初始化")
        
        generator = EntropyGenerator()
        
        # 验证初始化
        assert hasattr(generator, 'entropy_templates')
        assert hasattr(generator, 'noise_options')
        assert len(generator.entropy_templates) > 0
        assert len(generator.noise_options) > 0
        
        log_info("测试", "熵增生成器初始化测试通过")
    
    def test_generate_entropy_items(self):
        """测试生成熵增项目"""
        log_debug("测试", "开始测试生成熵增项目")
        
        generator = EntropyGenerator()
        
        # 测试生成不同数量的熵增项目
        items_2 = generator.generate_entropy_items(2)
        items_3 = generator.generate_entropy_items(3)
        
        # 验证结果
        assert len(items_2) == 2
        assert len(items_3) == 3
        assert all(isinstance(item, str) for item in items_2)
        assert all(len(item) > 0 for item in items_2)
        
        log_info("测试", "生成熵增项目测试通过", 生成数量=len(items_2))


class TestDNACompiler:
    """🧬 [DNA编译器测试] 测试DNA编译器集成功能"""
    
    def test_init_dna_compiler(self):
        """测试DNA编译器初始化"""
        log_debug("测试", "开始测试DNA编译器初始化")
        
        compiler = DNACompiler()
        
        # 验证初始化
        assert hasattr(compiler, 'shell_remover')
        assert hasattr(compiler, 'dehydrator')
        assert hasattr(compiler, 'gene_pairer')
        assert hasattr(compiler, 'entropy_generator')
        assert hasattr(compiler, 'compilation_stats')
        
        # 验证统计初始化
        stats = compiler.get_compilation_stats()
        assert stats["total_compiled"] == 0
        assert stats["successful_compilations"] == 0
        
        log_info("测试", "DNA编译器初始化测试通过")
    
    def test_compile_basic(self):
        """测试基础编译流程"""
        log_debug("测试", "开始测试基础编译流程")
        
        compiler = DNACompiler()
        
        emotion_tag = "恐惧"
        raw_text = """
        他感到强烈的恐惧，心跳像打鼓一样剧烈。
        她的呼吸变得急促，手掌开始出汗。
        周围的环境让人感到美丽而震撼。
        他看到了昏暗的光线，听到了奇怪的声音。
        """
        
        dna = compiler.compile_basic(emotion_tag, raw_text)
        
        # 验证结果
        assert isinstance(dna, EmotionalDNA)
        assert dna.emotion_tag == emotion_tag
        assert len(dna.physiological_reactions) > 0
        assert len(dna.sensory_triggers) > 0
        assert len(dna.entropy_items) > 0
        assert 0.0 <= dna.intensity_score <= 1.0
        assert 0.0 <= dna.reliability_score <= 1.0
        assert dna.purification_level == PurificationLevel.BASIC.value
        assert dna.source_text == raw_text
        
        # 验证统计更新
        stats = compiler.get_compilation_stats()
        assert stats["total_compiled"] == 1
        assert stats["successful_compilations"] == 1
        
        log_info("测试", "基础编译流程测试通过", 情感标签=dna.emotion_tag)
    
    def test_compilation_stats(self):
        """测试编译统计功能"""
        log_debug("测试", "开始测试编译统计功能")
        
        compiler = DNACompiler()
        
        # 执行多次编译
        test_cases = [
            ("恐惧", "他感到强烈的恐惧，心跳剧烈。"),
            ("愤怒", "她感到愤怒，脸色发红，拳头紧握。"),
            ("悲伤", "他感到深深的悲伤，眼泪流下。")
        ]
        
        for emotion_tag, raw_text in test_cases:
            dna = compiler.compile_basic(emotion_tag, raw_text)
            assert isinstance(dna, EmotionalDNA)
        
        # 验证统计
        stats = compiler.get_compilation_stats()
        assert stats["total_compiled"] == 3
        assert stats["successful_compilations"] == 3
        assert stats["failed_compilations"] == 0
        assert 0.0 <= stats["average_intensity"] <= 1.0
        assert 0.0 <= stats["average_reliability"] <= 1.0
        
        log_info("测试", "编译统计功能测试通过", 总编译数=stats["total_compiled"])


class TestEmotionalDNA:
    """🧬 [情感DNA测试] 测试情感DNA数据结构"""
    
    def test_emotional_dna_creation(self):
        """测试情感DNA创建"""
        log_debug("测试", "开始测试情感DNA创建")
        
        dna = EmotionalDNA(
            emotion_tag="恐惧",
            physiological_reactions=["心跳加速", "呼吸急促"],
            sensory_triggers=["看到阴影", "听到脚步声"],
            entropy_items=["周围传来嘈杂的声音"],
            intensity_score=0.8,
            reliability_score=0.7,
            purification_level=PurificationLevel.BASIC.value,
            source_text="测试文本"
        )
        
        # 验证创建
        assert dna.emotion_tag == "恐惧"
        assert len(dna.physiological_reactions) == 2
        assert len(dna.sensory_triggers) == 2
        assert len(dna.entropy_items) == 1
        assert dna.intensity_score == 0.8
        assert dna.reliability_score == 0.7
        
        log_info("测试", "情感DNA创建测试通过")
    
    def test_emotional_dna_to_dict(self):
        """测试情感DNA转换为字典"""
        log_debug("测试", "开始测试情感DNA转换为字典")
        
        dna = EmotionalDNA(
            emotion_tag="愤怒",
            physiological_reactions=["脸色发红"],
            sensory_triggers=["看到刺眼光线"],
            entropy_items=["空气闷热"],
            intensity_score=0.9,
            reliability_score=0.8,
            purification_level=PurificationLevel.ENHANCED.value,
            source_text="测试文本"
        )
        
        dna_dict = dna.to_dict()
        
        # 验证转换
        assert isinstance(dna_dict, dict)
        assert dna_dict["emotion_tag"] == "愤怒"
        assert dna_dict["intensity_score"] == 0.9
        assert dna_dict["reliability_score"] == 0.8
        assert dna_dict["purification_level"] == PurificationLevel.ENHANCED.value
        
        log_info("测试", "情感DNA转换为字典测试通过")


class TestDNACompilerIntegration:
    """🔄 [集成测试] DNA编译器集成测试"""
    
    def test_full_compilation_workflow(self):
        """测试完整编译工作流"""
        log_debug("测试", "开始测试完整编译工作流")
        
        compiler = DNACompiler()
        
        # 复杂测试文本
        complex_text = """
        当她看到那个背叛她的人时，愤怒像火山一样爆发了。
        她的心跳剧烈地跳动，仿佛要跳出胸膛。
        脸颊瞬间涨得通红，双手不由自主地握成拳头。
        她听到自己急促的呼吸声，感到血液在血管中沸腾。
        周围的世界变得模糊，只有那张可恨的脸清晰地印在眼前。
        她闻到了自己身上散发出的汗味，感到衣服贴在后背上。
        """
        
        dna = compiler.compile_basic("愤怒", complex_text)
        
        # 验证完整工作流结果
        assert isinstance(dna, EmotionalDNA)
        assert dna.emotion_tag == "愤怒"
        
        # 验证各个组件都有输出
        assert len(dna.physiological_reactions) > 0
        assert len(dna.sensory_triggers) > 0
        assert len(dna.entropy_items) > 0
        
        # 验证评分合理性
        assert dna.intensity_score > 0.5  # 复杂文本应该有较高强度
        assert dna.reliability_score > 0.0
        
        # 验证数据结构完整性
        dna_dict = dna.to_dict()
        required_fields = [
            "emotion_tag", "physiological_reactions", "sensory_triggers",
            "entropy_items", "intensity_score", "reliability_score",
            "purification_level", "source_text"
        ]
        for field in required_fields:
            assert field in dna_dict
        
        log_info("测试", "完整编译工作流测试通过", 
                情感标签=dna.emotion_tag, 
                生理反应数=len(dna.physiological_reactions),
                感官触发器数=len(dna.sensory_triggers),
                熵增项目数=len(dna.entropy_items))
